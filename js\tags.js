// 标签管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeTagsPage();
});

function initializeTagsPage() {
    bindTagEvents();
    loadTagStatistics();
    initializeTagAnimations();
}

function bindTagEvents() {
    // 创建标签按钮
    const createTagButton = document.getElementById('createTagButton');
    const addTagFab = document.getElementById('addTagFab');
    
    if (createTagButton) {
        createTagButton.addEventListener('click', showCreateTagDialog);
    }
    
    if (addTagFab) {
        addTagFab.addEventListener('click', showCreateTagDialog);
    }
    
    // 导入标签按钮
    const importTagsButton = document.getElementById('importTagsButton');
    if (importTagsButton) {
        importTagsButton.addEventListener('click', handleImportTags);
    }
    
    // 标签卡片点击事件
    const tagCards = document.querySelectorAll('.tag-card');
    tagCards.forEach(card => {
        card.addEventListener('click', handleTagCardClick);
    });
    
    // 标签菜单按钮
    const tagMenuButtons = document.querySelectorAll('.tag-menu');
    tagMenuButtons.forEach(button => {
        button.addEventListener('click', handleTagMenuClick);
    });
    
    // 搜索标签
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleTagSearch);
    }
}

function showCreateTagDialog() {
    const tagName = prompt('请输入新标签名称：');
    if (tagName && tagName.trim()) {
        createNewTag(tagName.trim());
    }
}

function createNewTag(name) {
    // 检查标签是否已存在
    const existingTags = document.querySelectorAll('.tag-card');
    const tagExists = Array.from(existingTags).some(card => {
        const tagName = card.querySelector('.tag-name')?.textContent;
        return tagName?.toLowerCase() === name.toLowerCase();
    });
    
    if (tagExists) {
        window.FileTagManager?.showToast('标签已存在', 'warning');
        return;
    }
    
    // 选择标签颜色
    const colors = ['work', 'important', 'personal', 'photos', 'music', 'reference'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    
    // 创建新标签卡片
    const newTagCard = createTagCardElement(name, randomColor, 0);
    
    // 添加到合适的分类中
    const personalSection = document.querySelector('.category-section:nth-child(2) .tag-grid');
    if (personalSection) {
        personalSection.appendChild(newTagCard);
        
        // 添加创建动画
        newTagCard.style.opacity = '0';
        newTagCard.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            newTagCard.style.transition = 'all 0.3s ease';
            newTagCard.style.opacity = '1';
            newTagCard.style.transform = 'translateY(0)';
        }, 100);
        
        // 绑定事件
        newTagCard.addEventListener('click', handleTagCardClick);
        newTagCard.querySelector('.tag-menu').addEventListener('click', handleTagMenuClick);
        
        window.FileTagManager?.showToast(`标签 "${name}" 创建成功`, 'success');
        updateTagStatistics();
    }
}

function createTagCardElement(name, color, fileCount) {
    const tagCard = document.createElement('div');
    tagCard.className = 'tag-card';
    tagCard.dataset.tagId = name.toLowerCase().replace(/\s+/g, '-');
    
    if (fileCount === 0) {
        tagCard.classList.add('unused');
    }
    
    tagCard.innerHTML = `
        <div class="tag-header">
            <div class="tag-color ${color}"></div>
            <h3 class="tag-name">${name}</h3>
            <button class="icon-button tag-menu">
                <span class="material-icons">more_vert</span>
            </button>
        </div>
        <div class="tag-info">
            <span class="file-count">${fileCount} 个文件</span>
            <span class="last-used ${fileCount === 0 ? 'unused-label' : ''}">${fileCount === 0 ? '未使用' : '最近使用：今天'}</span>
        </div>
    `;
    
    return tagCard;
}

function handleImportTags() {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json';
    fileInput.style.display = 'none';
    
    fileInput.addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const tags = JSON.parse(e.target.result);
                    importTagsFromData(tags);
                } catch (error) {
                    window.FileTagManager?.showToast('文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        }
    });
    
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
}

function importTagsFromData(tagsData) {
    if (!Array.isArray(tagsData)) {
        window.FileTagManager?.showToast('标签数据格式错误', 'error');
        return;
    }
    
    let importedCount = 0;
    
    tagsData.forEach(tagData => {
        if (tagData.name && tagData.color) {
            // 检查是否已存在
            const exists = document.querySelector(`[data-tag-id="${tagData.name.toLowerCase().replace(/\s+/g, '-')}"]`);
            if (!exists) {
                createNewTag(tagData.name);
                importedCount++;
            }
        }
    });
    
    window.FileTagManager?.showToast(`成功导入 ${importedCount} 个标签`, 'success');
}

function handleTagCardClick(event) {
    // 防止菜单按钮触发卡片点击
    if (event.target.closest('.tag-menu')) {
        return;
    }
    
    const tagCard = event.currentTarget;
    const tagName = tagCard.querySelector('.tag-name')?.textContent;
    const fileCount = tagCard.querySelector('.file-count')?.textContent;
    
    // 添加选中效果
    tagCard.classList.toggle('selected');
    
    // 添加点击动画
    window.FileTagManager?.animateElement(tagCard, 'pulse');
    
    // 跳转到筛选页面，显示该标签的文件
    setTimeout(() => {
        window.location.href = `filter.html?tag=${encodeURIComponent(tagName)}`;
    }, 300);
}

function handleTagMenuClick(event) {
    event.stopPropagation();
    
    const button = event.currentTarget;
    const tagCard = button.closest('.tag-card');
    const tagName = tagCard.querySelector('.tag-name')?.textContent;
    
    // 添加点击动画
    button.style.transform = 'scale(0.9)';
    setTimeout(() => {
        button.style.transform = '';
    }, 150);
    
    showTagMenu(tagName, tagCard);
}

function showTagMenu(tagName, tagCard) {
    const actions = ['编辑标签', '更改颜色', '导出标签', '删除标签'];
    const choice = prompt(`标签: ${tagName}\n\n选择操作：\n1. 编辑标签\n2. 更改颜色\n3. 导出标签\n4. 删除标签`);
    
    if (choice) {
        const actionIndex = parseInt(choice) - 1;
        switch (actionIndex) {
            case 0:
                editTag(tagName, tagCard);
                break;
            case 1:
                changeTagColor(tagCard);
                break;
            case 2:
                exportTag(tagName);
                break;
            case 3:
                deleteTag(tagName, tagCard);
                break;
        }
    }
}

function editTag(oldName, tagCard) {
    const newName = prompt('请输入新的标签名称：', oldName);
    if (newName && newName.trim() && newName !== oldName) {
        const tagNameElement = tagCard.querySelector('.tag-name');
        if (tagNameElement) {
            tagNameElement.textContent = newName.trim();
            tagCard.dataset.tagId = newName.toLowerCase().replace(/\s+/g, '-');
            window.FileTagManager?.showToast(`标签已重命名为 "${newName}"`, 'success');
        }
    }
}

function changeTagColor(tagCard) {
    const colors = ['work', 'important', 'personal', 'photos', 'music', 'reference', 'tutorial', 'notes'];
    const colorNames = ['蓝色', '橙色', '紫色', '绿色', '青色', '灰色', '粉色', '紫罗兰'];
    
    const choice = prompt(`选择新颜色：\n${colorNames.map((name, i) => `${i + 1}. ${name}`).join('\n')}`);
    
    if (choice) {
        const colorIndex = parseInt(choice) - 1;
        if (colorIndex >= 0 && colorIndex < colors.length) {
            const tagColorElement = tagCard.querySelector('.tag-color');
            if (tagColorElement) {
                // 移除旧颜色类
                colors.forEach(color => tagColorElement.classList.remove(color));
                // 添加新颜色类
                tagColorElement.classList.add(colors[colorIndex]);
                window.FileTagManager?.showToast(`颜色已更改为${colorNames[colorIndex]}`, 'success');
            }
        }
    }
}

function exportTag(tagName) {
    const tagData = {
        name: tagName,
        color: 'blue', // 简化处理
        createdAt: new Date().toISOString(),
        fileCount: 0
    };
    
    const dataStr = JSON.stringify([tagData], null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `tag-${tagName}.json`;
    link.click();
    
    window.FileTagManager?.showToast(`标签 "${tagName}" 已导出`, 'success');
}

function deleteTag(tagName, tagCard) {
    const confirmed = confirm(`确定要删除标签 "${tagName}" 吗？\n\n注意：这不会删除文件，只会移除标签。`);
    
    if (confirmed) {
        // 添加删除动画
        tagCard.style.transition = 'all 0.3s ease';
        tagCard.style.opacity = '0';
        tagCard.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            tagCard.remove();
            updateTagStatistics();
            window.FileTagManager?.showToast(`标签 "${tagName}" 已删除`, 'success');
        }, 300);
    }
}

function handleTagSearch(event) {
    const query = event.target.value.toLowerCase();
    const tagCards = document.querySelectorAll('.tag-card');
    
    tagCards.forEach(card => {
        const tagName = card.querySelector('.tag-name')?.textContent.toLowerCase();
        const matches = tagName?.includes(query);
        
        if (matches || query === '') {
            card.style.display = 'block';
            card.style.animation = 'fadeIn 0.3s ease';
        } else {
            card.style.display = 'none';
        }
    });
    
    // 更新分类计数
    updateCategoryCounts();
}

function loadTagStatistics() {
    // 模拟加载统计数据
    const stats = {
        totalTags: document.querySelectorAll('.tag-card').length,
        taggedFiles: 45,
        unusedTags: document.querySelectorAll('.tag-card.unused').length
    };
    
    updateStatisticsDisplay(stats);
}

function updateStatisticsDisplay(stats) {
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 3) {
        statNumbers[0].textContent = stats.totalTags;
        statNumbers[1].textContent = stats.taggedFiles;
        statNumbers[2].textContent = stats.unusedTags;
    }
}

function updateTagStatistics() {
    const totalTags = document.querySelectorAll('.tag-card').length;
    const unusedTags = document.querySelectorAll('.tag-card.unused').length;
    
    updateStatisticsDisplay({
        totalTags,
        taggedFiles: 45, // 保持不变
        unusedTags
    });
    
    updateCategoryCounts();
}

function updateCategoryCounts() {
    const categories = document.querySelectorAll('.category-section');
    categories.forEach(category => {
        const visibleTags = category.querySelectorAll('.tag-card:not([style*="display: none"])');
        const countElement = category.querySelector('.category-count');
        if (countElement) {
            countElement.textContent = visibleTags.length;
        }
    });
}

function initializeTagAnimations() {
    // 为标签卡片添加延迟动画
    const tagCards = document.querySelectorAll('.tag-card');
    tagCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // 监听滚动，为进入视口的元素添加动画
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'slideInUp 0.4s ease forwards';
            }
        });
    });
    
    document.querySelectorAll('.category-section').forEach(section => {
        observer.observe(section);
    });
}
