/* 标签管理页面专用样式 */

/* 标签统计卡片 */
.tag-stats {
    margin-bottom: 24px;
}

.stats-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow-1);
    display: flex;
    justify-content: space-around;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: 4px;
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
}

.action-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.action-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.action-button.primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
}

.action-button.secondary {
    background-color: var(--surface-color);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.action-button.secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
}

.action-button .material-icons {
    font-size: 20px;
}

/* 标签分类 */
.tag-categories {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.category-section {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow-1);
}

.category-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.category-title .material-icons {
    font-size: 24px;
    color: var(--primary-color);
}

.category-count {
    margin-left: auto;
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 标签网格 */
.tag-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

@media (max-width: 768px) {
    .tag-grid {
        grid-template-columns: 1fr;
    }
}

/* 标签卡片 */
.tag-card {
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.tag-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
    border-color: var(--primary-color);
}

.tag-card.unused {
    opacity: 0.6;
}

.tag-card.unused:hover {
    opacity: 1;
}

.tag-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.tag-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

/* 标签颜色定义 */
.tag-color.work { background-color: #1976d2; }
.tag-color.important { background-color: #f57c00; }
.tag-color.project { background-color: #388e3c; }
.tag-color.meeting { background-color: #7b1fa2; }
.tag-color.personal { background-color: #c2185b; }
.tag-color.photos { background-color: #00796b; }
.tag-color.music { background-color: #0277bd; }
.tag-color.favorite { background-color: #f9a825; }
.tag-color.travel { background-color: #5d4037; }
.tag-color.reference { background-color: #455a64; }
.tag-color.tutorial { background-color: #e91e63; }
.tag-color.notes { background-color: #9c27b0; }

.tag-name {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.tag-menu {
    opacity: 0;
    transition: opacity 0.2s;
}

.tag-card:hover .tag-menu {
    opacity: 1;
}

.tag-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.file-count {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.last-used {
    font-size: 12px;
    color: var(--text-secondary);
}

.unused-label {
    color: var(--error-color);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .stats-card {
        padding: 16px;
    }
    
    .stat-number {
        font-size: 24px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .action-button {
        padding: 16px;
    }
    
    .category-section {
        padding: 16px;
    }
    
    .tag-card {
        padding: 12px;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.category-section {
    animation: slideInUp 0.4s ease;
}

.tag-card {
    animation: slideInUp 0.3s ease;
}

/* 标签卡片的延迟动画 */
.tag-card:nth-child(1) { animation-delay: 0.1s; }
.tag-card:nth-child(2) { animation-delay: 0.2s; }
.tag-card:nth-child(3) { animation-delay: 0.3s; }
.tag-card:nth-child(4) { animation-delay: 0.4s; }
.tag-card:nth-child(5) { animation-delay: 0.5s; }

/* 标签选择状态 */
.tag-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.1);
}

.tag-card.selected::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    border-radius: 50%;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
    background-size: 12px;
    background-repeat: no-repeat;
    background-position: center;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-state .material-icons {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}
