/* 设置页面专用样式 */

/* 用户资料卡片 */
.user-profile {
    margin-bottom: 24px;
}

.profile-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    box-shadow: var(--shadow-1);
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
}

.avatar {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.avatar .material-icons {
    font-size: 32px;
    color: white;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.user-email {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.storage-info {
    font-size: 12px;
    color: var(--text-secondary);
}

.edit-profile-button {
    width: 48px;
    height: 48px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-secondary);
}

.edit-profile-button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.storage-bar {
    height: 6px;
    background-color: var(--divider-color);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 24px;
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 设置区域 */
.settings-section {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-1);
}

.section-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--divider-color);
}

.settings-list {
    display: flex;
    flex-direction: column;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid var(--divider-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.setting-info .material-icons {
    font-size: 24px;
    color: var(--text-secondary);
    width: 24px;
    flex-shrink: 0;
}

.setting-text {
    flex: 1;
}

.setting-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.setting-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 设置控件 */
.setting-select {
    padding: 8px 12px;
    border: 1px solid var(--divider-color);
    border-radius: 6px;
    background-color: var(--background-color);
    color: var(--text-primary);
    font-size: 14px;
    min-width: 120px;
    cursor: pointer;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 切换开关 */
.toggle-switch {
    position: relative;
    width: 52px;
    height: 32px;
}

.toggle-switch input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--divider-color);
    border-radius: 16px;
    transition: all 0.3s;
}

.toggle-label:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .toggle-label {
    background-color: var(--primary-color);
}

input:checked + .toggle-label:before {
    transform: translateX(20px);
}

/* 操作按钮 */
.action-button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 80px;
}

.action-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.action-button.primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
}

.action-button.secondary {
    background-color: var(--background-color);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.action-button.secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
}

.action-button.danger {
    background-color: var(--error-color);
    color: white;
}

.action-button.danger:hover {
    background-color: #d32f2f;
    transform: translateY(-1px);
    box-shadow: var(--shadow-2);
}

/* 特殊设置项样式 */
.toggle-item .setting-info {
    cursor: pointer;
}

.action-item {
    align-items: flex-start;
    padding: 20px 0;
}

.action-item .setting-info {
    margin-right: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .profile-card {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .user-info {
        order: 1;
    }
    
    .edit-profile-button {
        order: 2;
        align-self: center;
    }
    
    .avatar {
        order: 0;
        align-self: center;
    }
}

@media (max-width: 480px) {
    .user-profile,
    .settings-section {
        margin-left: -4px;
        margin-right: -4px;
    }
    
    .profile-card,
    .settings-section {
        padding: 16px;
        border-radius: 8px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px 0;
    }
    
    .action-item {
        align-items: flex-start;
    }
    
    .action-item .setting-info {
        margin-right: 0;
        margin-bottom: 12px;
    }
    
    .setting-select,
    .action-button {
        width: 100%;
    }
    
    .toggle-switch {
        align-self: flex-end;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-profile,
.settings-section {
    animation: slideInUp 0.4s ease;
}

.user-profile { animation-delay: 0.1s; }
.settings-section:nth-child(2) { animation-delay: 0.2s; }
.settings-section:nth-child(3) { animation-delay: 0.3s; }
.settings-section:nth-child(4) { animation-delay: 0.4s; }
.settings-section:nth-child(5) { animation-delay: 0.5s; }
.settings-section:nth-child(6) { animation-delay: 0.6s; }

/* 设置项悬停效果 */
.setting-item {
    transition: background-color 0.2s;
    border-radius: 8px;
    margin: 0 -12px;
    padding-left: 12px;
    padding-right: 12px;
}

.setting-item:hover {
    background-color: rgba(25, 118, 210, 0.05);
}

/* 深色模式特殊处理 */
@media (prefers-color-scheme: dark) {
    .toggle-label:before {
        background-color: #f5f5f5;
    }
    
    .storage-bar {
        background-color: #333333;
    }
    
    .setting-item:hover {
        background-color: rgba(66, 165, 245, 0.1);
    }
}
