<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件详情 - 文件标签管理器</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/file-detail.css">
</head>
<body>
    <!-- 顶部应用栏 -->
    <header class="app-bar">
        <div class="app-bar-content">
            <button class="icon-button back-button" onclick="history.back()">
                <span class="material-icons">arrow_back</span>
            </button>
            <h1 class="app-title">文件详情</h1>
            <button class="icon-button share-button">
                <span class="material-icons">share</span>
            </button>
            <button class="icon-button more-button">
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 文件预览区域 -->
        <section class="file-preview">
            <div class="preview-container">
                <div class="file-icon-large">
                    <span class="material-icons">description</span>
                </div>
                <div class="file-type-badge">DOCX</div>
            </div>
        </section>

        <!-- 文件基本信息 -->
        <section class="file-info-card">
            <h2 class="file-title">项目计划书.docx</h2>
            <div class="file-metadata">
                <div class="metadata-item">
                    <span class="material-icons">folder</span>
                    <span class="metadata-label">位置</span>
                    <span class="metadata-value">/Documents/Work</span>
                </div>
                <div class="metadata-item">
                    <span class="material-icons">storage</span>
                    <span class="metadata-label">大小</span>
                    <span class="metadata-value">2.3 MB</span>
                </div>
                <div class="metadata-item">
                    <span class="material-icons">schedule</span>
                    <span class="metadata-label">修改时间</span>
                    <span class="metadata-value">2024年1月15日 14:30</span>
                </div>
                <div class="metadata-item">
                    <span class="material-icons">person</span>
                    <span class="metadata-label">创建者</span>
                    <span class="metadata-value">张三</span>
                </div>
            </div>
        </section>

        <!-- 标签管理区域 -->
        <section class="tags-section">
            <div class="section-header">
                <h3 class="section-title">
                    <span class="material-icons">label</span>
                    文件标签
                </h3>
                <button class="icon-button add-tag-button" id="addTagButton">
                    <span class="material-icons">add</span>
                </button>
            </div>
            
            <div class="current-tags">
                <div class="tag-item" data-tag="work">
                    <span class="tag-color work"></span>
                    <span class="tag-name">工作</span>
                    <button class="icon-button remove-tag">
                        <span class="material-icons">close</span>
                    </button>
                </div>
                <div class="tag-item" data-tag="important">
                    <span class="tag-color important"></span>
                    <span class="tag-name">重要</span>
                    <button class="icon-button remove-tag">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            </div>

            <!-- 添加标签界面 -->
            <div class="add-tag-panel" id="addTagPanel">
                <div class="search-tags">
                    <input type="text" placeholder="搜索或创建标签..." class="tag-search-input" id="tagSearchInput">
                </div>
                <div class="suggested-tags">
                    <h4 class="suggestions-title">建议标签</h4>
                    <div class="tag-suggestions">
                        <button class="suggestion-tag" data-tag="project">
                            <span class="tag-color project"></span>
                            项目
                        </button>
                        <button class="suggestion-tag" data-tag="meeting">
                            <span class="tag-color meeting"></span>
                            会议
                        </button>
                        <button class="suggestion-tag" data-tag="reference">
                            <span class="tag-color reference"></span>
                            参考
                        </button>
                        <button class="suggestion-tag" data-tag="urgent">
                            <span class="tag-color urgent"></span>
                            紧急
                        </button>
                    </div>
                </div>
                <div class="all-tags">
                    <h4 class="suggestions-title">所有标签</h4>
                    <div class="tag-list">
                        <button class="tag-option" data-tag="personal">
                            <span class="tag-color personal"></span>
                            <span class="tag-name">个人</span>
                            <span class="file-count">20个文件</span>
                        </button>
                        <button class="tag-option" data-tag="photos">
                            <span class="tag-color photos"></span>
                            <span class="tag-name">照片</span>
                            <span class="file-count">35个文件</span>
                        </button>
                        <button class="tag-option" data-tag="music">
                            <span class="tag-color music"></span>
                            <span class="tag-name">音乐</span>
                            <span class="file-count">18个文件</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 文件操作 -->
        <section class="file-actions">
            <div class="action-grid">
                <button class="action-item">
                    <span class="material-icons">open_in_new</span>
                    <span class="action-label">打开</span>
                </button>
                <button class="action-item">
                    <span class="material-icons">edit</span>
                    <span class="action-label">编辑</span>
                </button>
                <button class="action-item">
                    <span class="material-icons">share</span>
                    <span class="action-label">分享</span>
                </button>
                <button class="action-item">
                    <span class="material-icons">file_copy</span>
                    <span class="action-label">复制</span>
                </button>
                <button class="action-item">
                    <span class="material-icons">drive_file_move</span>
                    <span class="action-label">移动</span>
                </button>
                <button class="action-item">
                    <span class="material-icons">star</span>
                    <span class="action-label">收藏</span>
                </button>
                <button class="action-item">
                    <span class="material-icons">file_download</span>
                    <span class="action-label">下载</span>
                </button>
                <button class="action-item danger">
                    <span class="material-icons">delete</span>
                    <span class="action-label">删除</span>
                </button>
            </div>
        </section>

        <!-- 相关文件 -->
        <section class="related-files">
            <h3 class="section-title">
                <span class="material-icons">link</span>
                相关文件
            </h3>
            <div class="related-list">
                <div class="related-item">
                    <div class="file-icon">
                        <span class="material-icons">picture_as_pdf</span>
                    </div>
                    <div class="file-info">
                        <h4 class="file-name">项目需求文档.pdf</h4>
                        <p class="file-details">共同标签：工作、重要</p>
                    </div>
                    <button class="icon-button">
                        <span class="material-icons">arrow_forward</span>
                    </button>
                </div>
                <div class="related-item">
                    <div class="file-icon">
                        <span class="material-icons">table_chart</span>
                    </div>
                    <div class="file-info">
                        <h4 class="file-name">项目时间表.xlsx</h4>
                        <p class="file-details">共同标签：工作</p>
                    </div>
                    <button class="icon-button">
                        <span class="material-icons">arrow_forward</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- 文件历史 -->
        <section class="file-history">
            <h3 class="section-title">
                <span class="material-icons">history</span>
                最近活动
            </h3>
            <div class="history-list">
                <div class="history-item">
                    <div class="history-icon">
                        <span class="material-icons">edit</span>
                    </div>
                    <div class="history-info">
                        <p class="history-action">文件已修改</p>
                        <p class="history-time">今天 14:30</p>
                    </div>
                </div>
                <div class="history-item">
                    <div class="history-icon">
                        <span class="material-icons">label</span>
                    </div>
                    <div class="history-info">
                        <p class="history-action">添加标签"重要"</p>
                        <p class="history-time">今天 10:15</p>
                    </div>
                </div>
                <div class="history-item">
                    <div class="history-icon">
                        <span class="material-icons">add</span>
                    </div>
                    <div class="history-info">
                        <p class="history-action">文件已创建</p>
                        <p class="history-time">1月10日 09:20</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-navigation">
        <a href="index.html" class="nav-item">
            <span class="material-icons">folder</span>
            <span class="nav-label">文件</span>
        </a>
        <a href="tags.html" class="nav-item">
            <span class="material-icons">label</span>
            <span class="nav-label">标签</span>
        </a>
        <a href="filter.html" class="nav-item">
            <span class="material-icons">filter_list</span>
            <span class="nav-label">筛选</span>
        </a>
        <a href="settings.html" class="nav-item">
            <span class="material-icons">settings</span>
            <span class="nav-label">设置</span>
        </a>
    </nav>

    <script src="js/main.js"></script>
    <script src="js/file-detail.js"></script>
</body>
</html>
