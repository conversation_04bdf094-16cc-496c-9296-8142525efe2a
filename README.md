# Android 文件标签管理器 - Web界面原型

这是一个完整的Android文件标签管理器应用的Web界面原型，使用现代Web技术栈实现，遵循Material Design设计规范。

## 🚀 功能特性

### 核心功能
- **文件列表管理** - 显示所有文件及其标签信息
- **标签管理系统** - 创建、编辑、删除和组织标签
- **智能筛选** - 多维度筛选和搜索文件
- **文件详情查看** - 查看文件元数据和管理标签
- **个性化设置** - 主题、语言、同步等配置

### 设计特色
- **Material Design** - 遵循Google Material Design 3.0规范
- **响应式设计** - 完美适配Android手机屏幕
- **深色模式支持** - 自动跟随系统主题或手动切换
- **流畅动画** - 丰富的交互动画和过渡效果
- **触摸优化** - 针对移动设备的触摸交互优化

## 📁 项目结构

```
android-file-tag-manager/
├── index.html              # 主页面 - 文件列表
├── tags.html              # 标签管理页面
├── file-detail.html       # 文件详情页面
├── filter.html            # 标签筛选页面
├── settings.html          # 设置页面
├── css/
│   ├── styles.css         # 主要样式文件
│   ├── tags.css          # 标签页面样式
│   ├── file-detail.css   # 文件详情页面样式
│   ├── filter.css        # 筛选页面样式
│   └── settings.css      # 设置页面样式
├── js/
│   ├── main.js           # 主要JavaScript逻辑
│   ├── tags.js           # 标签管理功能
│   ├── file-detail.js    # 文件详情功能
│   ├── filter.js         # 筛选功能
│   └── settings.js       # 设置功能
└── README.md             # 项目说明文档
```

## 🎨 页面介绍

### 1. 主页面 (index.html)
- **文件列表展示** - 卡片式布局显示文件信息
- **快速筛选** - 顶部标签快速筛选文件
- **搜索功能** - 实时搜索文件名和标签
- **底部导航** - Material Design风格的底部导航栏

### 2. 标签管理页面 (tags.html)
- **标签统计** - 显示标签总数、已标记文件数等
- **分类管理** - 按工作、个人、学习等分类组织标签
- **标签操作** - 创建、编辑、删除、导入导出标签
- **使用情况** - 显示每个标签的使用频率和最近使用时间

### 3. 文件详情页面 (file-detail.html)
- **文件预览** - 大图标和文件类型显示
- **元数据信息** - 文件大小、位置、修改时间等
- **标签管理** - 添加、移除文件标签
- **文件操作** - 打开、编辑、分享、删除等操作
- **相关文件** - 显示具有相同标签的相关文件

### 4. 标签筛选页面 (filter.html)
- **多维度筛选** - 按标签、类型、时间、大小筛选
- **筛选模式** - 支持"任意标签"和"所有标签"模式
- **排序选项** - 按名称、时间、大小、类型排序
- **视图切换** - 列表视图和网格视图切换

### 5. 设置页面 (settings.html)
- **用户资料** - 用户信息和存储使用情况
- **应用设置** - 主题、语言、默认视图等
- **标签设置** - 智能建议、颜色显示、数量限制
- **数据管理** - 备份、恢复、同步设置
- **隐私安全** - 应用锁、使用统计等

## 🛠️ 技术实现

### 前端技术栈
- **HTML5** - 语义化标记和现代Web标准
- **CSS3** - Flexbox、Grid、CSS变量、动画
- **Vanilla JavaScript** - 原生JS实现所有交互功能
- **Material Icons** - Google Material Design图标库
- **Roboto字体** - Material Design推荐字体

### 设计规范
- **颜色系统** - 基于Material Design的颜色规范
- **间距系统** - 8dp网格系统
- **字体层级** - Material Design字体大小和权重
- **阴影系统** - 分层阴影效果
- **动画曲线** - Material Motion动画规范

### 响应式设计
- **移动优先** - 针对Android手机屏幕优化
- **触摸友好** - 44dp最小触摸目标
- **手势支持** - 滑动、点击、长按等手势
- **屏幕适配** - 支持不同分辨率和屏幕密度

## 🚀 快速开始

### 本地预览
1. 克隆或下载项目文件
2. 使用现代浏览器打开 `index.html`
3. 或使用本地服务器（推荐）：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   
   # 使用PHP
   php -S localhost:8000
   ```

### 移动设备预览
1. 在浏览器中打开开发者工具
2. 切换到移动设备模拟模式
3. 选择Android设备（如Pixel 5）
4. 体验完整的移动端交互

## 📱 功能演示

### 主要交互流程
1. **浏览文件** - 在主页面查看文件列表
2. **筛选文件** - 使用快速筛选或进入筛选页面
3. **查看详情** - 点击文件进入详情页面
4. **管理标签** - 在详情页面添加或移除标签
5. **标签管理** - 在标签页面创建和组织标签
6. **个性化设置** - 在设置页面调整应用偏好

### 特色功能体验
- **搜索功能** - 点击搜索图标体验实时搜索
- **标签筛选** - 在筛选页面体验多维度筛选
- **主题切换** - 在设置中切换深色/浅色主题
- **动画效果** - 体验流畅的页面切换和交互动画

## 🎯 设计亮点

### 用户体验
- **直观导航** - 清晰的信息架构和导航结构
- **快速操作** - 常用功能的快捷访问
- **视觉反馈** - 丰富的交互反馈和状态提示
- **错误处理** - 友好的错误提示和引导

### 视觉设计
- **一致性** - 统一的设计语言和视觉风格
- **层次感** - 清晰的信息层级和视觉重点
- **品质感** - 精致的细节和高质量的视觉效果
- **可访问性** - 良好的对比度和可读性

## 🔧 自定义和扩展

### 主题定制
- 修改 `css/styles.css` 中的CSS变量来调整颜色主题
- 添加新的颜色方案和主题变体

### 功能扩展
- 在相应的JavaScript文件中添加新功能
- 扩展标签系统和筛选逻辑
- 集成真实的文件系统API

### 本地化
- 修改HTML中的文本内容
- 在JavaScript中实现多语言支持
- 添加RTL语言支持

## 📄 许可证

本项目仅用于界面设计演示和原型展示，请根据实际需求进行修改和使用。

## 🤝 贡献

欢迎提出改进建议和功能需求，帮助完善这个文件标签管理器的界面设计。

---

**注意**: 这是一个纯前端原型，不包含真实的文件系统操作。所有数据都是模拟数据，用于演示界面功能和交互效果。
