<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 文件标签管理器</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/settings.css">
</head>
<body>
    <!-- 顶部应用栏 -->
    <header class="app-bar">
        <div class="app-bar-content">
            <button class="icon-button back-button" onclick="history.back()">
                <span class="material-icons">arrow_back</span>
            </button>
            <h1 class="app-title">设置</h1>
            <button class="icon-button help-button">
                <span class="material-icons">help_outline</span>
            </button>
            <button class="icon-button more-button">
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 用户信息 -->
        <section class="user-profile">
            <div class="profile-card">
                <div class="avatar">
                    <span class="material-icons">person</span>
                </div>
                <div class="user-info">
                    <h2 class="user-name">张三</h2>
                    <p class="user-email"><EMAIL></p>
                    <p class="storage-info">已使用 2.3 GB / 15 GB</p>
                </div>
                <button class="edit-profile-button">
                    <span class="material-icons">edit</span>
                </button>
            </div>
            <div class="storage-bar">
                <div class="storage-used" style="width: 15.3%"></div>
            </div>
        </section>

        <!-- 应用设置 -->
        <section class="settings-section">
            <h3 class="section-title">应用设置</h3>
            <div class="settings-list">
                <div class="setting-item">
                    <div class="setting-info">
                        <span class="material-icons">palette</span>
                        <div class="setting-text">
                            <h4 class="setting-name">主题</h4>
                            <p class="setting-description">选择应用外观主题</p>
                        </div>
                    </div>
                    <select class="setting-select" id="themeSelect">
                        <option value="auto">跟随系统</option>
                        <option value="light">浅色模式</option>
                        <option value="dark">深色模式</option>
                    </select>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <span class="material-icons">language</span>
                        <div class="setting-text">
                            <h4 class="setting-name">语言</h4>
                            <p class="setting-description">选择应用显示语言</p>
                        </div>
                    </div>
                    <select class="setting-select" id="languageSelect">
                        <option value="zh-CN" selected>简体中文</option>
                        <option value="zh-TW">繁体中文</option>
                        <option value="en">English</option>
                    </select>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <span class="material-icons">view_list</span>
                        <div class="setting-text">
                            <h4 class="setting-name">默认视图</h4>
                            <p class="setting-description">文件列表的默认显示方式</p>
                        </div>
                    </div>
                    <select class="setting-select" id="defaultViewSelect">
                        <option value="list" selected>列表视图</option>
                        <option value="grid">网格视图</option>
                        <option value="card">卡片视图</option>
                    </select>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <span class="material-icons">sort</span>
                        <div class="setting-text">
                            <h4 class="setting-name">默认排序</h4>
                            <p class="setting-description">文件列表的默认排序方式</p>
                        </div>
                    </div>
                    <select class="setting-select" id="defaultSortSelect">
                        <option value="name">按名称</option>
                        <option value="date" selected>按修改时间</option>
                        <option value="size">按大小</option>
                        <option value="type">按类型</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- 标签设置 -->
        <section class="settings-section">
            <h3 class="section-title">标签设置</h3>
            <div class="settings-list">
                <div class="setting-item toggle-item">
                    <div class="setting-info">
                        <span class="material-icons">auto_awesome</span>
                        <div class="setting-text">
                            <h4 class="setting-name">智能标签建议</h4>
                            <p class="setting-description">根据文件内容自动建议标签</p>
                        </div>
                    </div>
                    <div class="toggle-switch">
                        <input type="checkbox" id="smartTags" checked>
                        <label for="smartTags" class="toggle-label"></label>
                    </div>
                </div>

                <div class="setting-item toggle-item">
                    <div class="setting-info">
                        <span class="material-icons">label</span>
                        <div class="setting-text">
                            <h4 class="setting-name">显示标签颜色</h4>
                            <p class="setting-description">在文件列表中显示标签颜色</p>
                        </div>
                    </div>
                    <div class="toggle-switch">
                        <input type="checkbox" id="showTagColors" checked>
                        <label for="showTagColors" class="toggle-label"></label>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-info">
                        <span class="material-icons">numbers</span>
                        <div class="setting-text">
                            <h4 class="setting-name">最大标签数量</h4>
                            <p class="setting-description">每个文件最多可添加的标签数</p>
                        </div>
                    </div>
                    <select class="setting-select" id="maxTagsSelect">
                        <option value="3">3个</option>
                        <option value="5" selected>5个</option>
                        <option value="10">10个</option>
                        <option value="unlimited">无限制</option>
                    </select>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">file_download</span>
                        <div class="setting-text">
                            <h4 class="setting-name">导出标签</h4>
                            <p class="setting-description">将所有标签导出为JSON文件</p>
                        </div>
                    </div>
                    <button class="action-button secondary">导出</button>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">file_upload</span>
                        <div class="setting-text">
                            <h4 class="setting-name">导入标签</h4>
                            <p class="setting-description">从JSON文件导入标签配置</p>
                        </div>
                    </div>
                    <button class="action-button secondary">导入</button>
                </div>
            </div>
        </section>

        <!-- 存储设置 -->
        <section class="settings-section">
            <h3 class="section-title">存储与同步</h3>
            <div class="settings-list">
                <div class="setting-item toggle-item">
                    <div class="setting-info">
                        <span class="material-icons">cloud_sync</span>
                        <div class="setting-text">
                            <h4 class="setting-name">云端同步</h4>
                            <p class="setting-description">自动同步标签到云端</p>
                        </div>
                    </div>
                    <div class="toggle-switch">
                        <input type="checkbox" id="cloudSync" checked>
                        <label for="cloudSync" class="toggle-label"></label>
                    </div>
                </div>

                <div class="setting-item toggle-item">
                    <div class="setting-info">
                        <span class="material-icons">wifi</span>
                        <div class="setting-text">
                            <h4 class="setting-name">仅WiFi同步</h4>
                            <p class="setting-description">只在WiFi环境下进行同步</p>
                        </div>
                    </div>
                    <div class="toggle-switch">
                        <input type="checkbox" id="wifiOnlySync" checked>
                        <label for="wifiOnlySync" class="toggle-label"></label>
                    </div>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">backup</span>
                        <div class="setting-text">
                            <h4 class="setting-name">备份数据</h4>
                            <p class="setting-description">创建完整的数据备份</p>
                        </div>
                    </div>
                    <button class="action-button primary">立即备份</button>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">restore</span>
                        <div class="setting-text">
                            <h4 class="setting-name">恢复数据</h4>
                            <p class="setting-description">从备份文件恢复数据</p>
                        </div>
                    </div>
                    <button class="action-button secondary">选择备份</button>
                </div>
            </div>
        </section>

        <!-- 隐私与安全 -->
        <section class="settings-section">
            <h3 class="section-title">隐私与安全</h3>
            <div class="settings-list">
                <div class="setting-item toggle-item">
                    <div class="setting-info">
                        <span class="material-icons">analytics</span>
                        <div class="setting-text">
                            <h4 class="setting-name">使用统计</h4>
                            <p class="setting-description">帮助改进应用体验</p>
                        </div>
                    </div>
                    <div class="toggle-switch">
                        <input type="checkbox" id="analytics" checked>
                        <label for="analytics" class="toggle-label"></label>
                    </div>
                </div>

                <div class="setting-item toggle-item">
                    <div class="setting-info">
                        <span class="material-icons">lock</span>
                        <div class="setting-text">
                            <h4 class="setting-name">应用锁定</h4>
                            <p class="setting-description">使用指纹或密码保护应用</p>
                        </div>
                    </div>
                    <div class="toggle-switch">
                        <input type="checkbox" id="appLock">
                        <label for="appLock" class="toggle-label"></label>
                    </div>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">delete_sweep</span>
                        <div class="setting-text">
                            <h4 class="setting-name">清除缓存</h4>
                            <p class="setting-description">清除应用缓存数据</p>
                        </div>
                    </div>
                    <button class="action-button secondary">清除</button>
                </div>
            </div>
        </section>

        <!-- 关于应用 -->
        <section class="settings-section">
            <h3 class="section-title">关于</h3>
            <div class="settings-list">
                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">info</span>
                        <div class="setting-text">
                            <h4 class="setting-name">版本信息</h4>
                            <p class="setting-description">文件标签管理器 v1.0.0</p>
                        </div>
                    </div>
                    <button class="action-button secondary">检查更新</button>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">help</span>
                        <div class="setting-text">
                            <h4 class="setting-name">帮助与支持</h4>
                            <p class="setting-description">查看使用指南和常见问题</p>
                        </div>
                    </div>
                    <button class="action-button secondary">查看</button>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">feedback</span>
                        <div class="setting-text">
                            <h4 class="setting-name">反馈建议</h4>
                            <p class="setting-description">向我们发送反馈和建议</p>
                        </div>
                    </div>
                    <button class="action-button secondary">发送反馈</button>
                </div>

                <div class="setting-item action-item">
                    <div class="setting-info">
                        <span class="material-icons">gavel</span>
                        <div class="setting-text">
                            <h4 class="setting-name">法律信息</h4>
                            <p class="setting-description">隐私政策和使用条款</p>
                        </div>
                    </div>
                    <button class="action-button secondary">查看</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-navigation">
        <a href="index.html" class="nav-item">
            <span class="material-icons">folder</span>
            <span class="nav-label">文件</span>
        </a>
        <a href="tags.html" class="nav-item">
            <span class="material-icons">label</span>
            <span class="nav-label">标签</span>
        </a>
        <a href="filter.html" class="nav-item">
            <span class="material-icons">filter_list</span>
            <span class="nav-label">筛选</span>
        </a>
        <a href="settings.html" class="nav-item active">
            <span class="material-icons">settings</span>
            <span class="nav-label">设置</span>
        </a>
    </nav>

    <script src="js/main.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
