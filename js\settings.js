// 设置页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeSettingsPage();
});

function initializeSettingsPage() {
    loadSettings();
    bindSettingsEvents();
    initializeStorageInfo();
}

function loadSettings() {
    // 从localStorage加载设置
    const settings = {
        theme: localStorage.getItem('theme') || 'auto',
        language: localStorage.getItem('language') || 'zh-CN',
        defaultView: localStorage.getItem('defaultView') || 'list',
        defaultSort: localStorage.getItem('defaultSort') || 'date',
        smartTags: localStorage.getItem('smartTags') !== 'false',
        showTagColors: localStorage.getItem('showTagColors') !== 'false',
        maxTags: localStorage.getItem('maxTags') || '5',
        cloudSync: localStorage.getItem('cloudSync') !== 'false',
        wifiOnlySync: localStorage.getItem('wifiOnlySync') !== 'false',
        analytics: localStorage.getItem('analytics') !== 'false',
        appLock: localStorage.getItem('appLock') === 'true'
    };
    
    // 应用设置到界面
    applySettingsToUI(settings);
}

function applySettingsToUI(settings) {
    // 下拉选择框
    const selects = {
        'themeSelect': settings.theme,
        'languageSelect': settings.language,
        'defaultViewSelect': settings.defaultView,
        'defaultSortSelect': settings.defaultSort,
        'maxTagsSelect': settings.maxTags
    };
    
    Object.entries(selects).forEach(([id, value]) => {
        const select = document.getElementById(id);
        if (select) {
            select.value = value;
        }
    });
    
    // 切换开关
    const toggles = {
        'smartTags': settings.smartTags,
        'showTagColors': settings.showTagColors,
        'cloudSync': settings.cloudSync,
        'wifiOnlySync': settings.wifiOnlySync,
        'analytics': settings.analytics,
        'appLock': settings.appLock
    };
    
    Object.entries(toggles).forEach(([id, value]) => {
        const toggle = document.getElementById(id);
        if (toggle) {
            toggle.checked = value;
        }
    });
}

function bindSettingsEvents() {
    // 编辑资料按钮
    const editProfileButton = document.querySelector('.edit-profile-button');
    if (editProfileButton) {
        editProfileButton.addEventListener('click', handleEditProfile);
    }
    
    // 设置选择框变化
    const settingSelects = document.querySelectorAll('.setting-select');
    settingSelects.forEach(select => {
        select.addEventListener('change', handleSettingChange);
    });
    
    // 切换开关变化
    const toggleSwitches = document.querySelectorAll('.toggle-switch input[type="checkbox"]');
    toggleSwitches.forEach(toggle => {
        toggle.addEventListener('change', handleToggleChange);
    });
    
    // 操作按钮
    const actionButtons = document.querySelectorAll('.action-button');
    actionButtons.forEach(button => {
        button.addEventListener('click', handleActionButton);
    });
    
    // 设置项点击（用于切换开关）
    const toggleItems = document.querySelectorAll('.toggle-item .setting-info');
    toggleItems.forEach(item => {
        item.addEventListener('click', handleToggleItemClick);
    });
}

function handleEditProfile() {
    const userName = document.querySelector('.user-name')?.textContent;
    const userEmail = document.querySelector('.user-email')?.textContent;
    
    const newName = prompt('请输入新的用户名：', userName);
    if (newName && newName.trim() && newName !== userName) {
        const userNameElement = document.querySelector('.user-name');
        if (userNameElement) {
            userNameElement.textContent = newName.trim();
            localStorage.setItem('userName', newName.trim());
            window.FileTagManager?.showToast('用户名已更新', 'success');
        }
    }
}

function handleSettingChange(event) {
    const select = event.target;
    const settingName = select.id.replace('Select', '');
    const value = select.value;
    
    // 保存设置
    localStorage.setItem(settingName, value);
    
    // 应用设置
    applySettingChange(settingName, value);
    
    window.FileTagManager?.showToast('设置已保存', 'success');
}

function handleToggleChange(event) {
    const toggle = event.target;
    const settingName = toggle.id;
    const value = toggle.checked;
    
    // 保存设置
    localStorage.setItem(settingName, value.toString());
    
    // 应用设置
    applySettingChange(settingName, value);
    
    window.FileTagManager?.showToast('设置已保存', 'success');
}

function handleToggleItemClick(event) {
    const settingItem = event.currentTarget.closest('.setting-item');
    const toggle = settingItem.querySelector('input[type="checkbox"]');
    
    if (toggle) {
        toggle.checked = !toggle.checked;
        handleToggleChange({ target: toggle });
    }
}

function applySettingChange(settingName, value) {
    switch (settingName) {
        case 'theme':
            applyTheme(value);
            break;
        case 'language':
            applyLanguage(value);
            break;
        case 'cloudSync':
            handleCloudSyncChange(value);
            break;
        case 'appLock':
            handleAppLockChange(value);
            break;
        default:
            console.log(`设置 ${settingName} 已更改为 ${value}`);
    }
}

function applyTheme(theme) {
    const body = document.body;
    
    switch (theme) {
        case 'light':
            body.classList.remove('dark-theme');
            break;
        case 'dark':
            body.classList.add('dark-theme');
            break;
        case 'auto':
            const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (systemDark) {
                body.classList.add('dark-theme');
            } else {
                body.classList.remove('dark-theme');
            }
            break;
    }
}

function applyLanguage(language) {
    // 这里可以实现语言切换逻辑
    console.log(`语言已切换为: ${language}`);
    // 实际应用中需要重新加载页面或动态更新文本
}

function handleCloudSyncChange(enabled) {
    if (enabled) {
        // 模拟开启云端同步
        window.FileTagManager?.showToast('正在连接云端服务...', 'info');
        setTimeout(() => {
            window.FileTagManager?.showToast('云端同步已开启', 'success');
        }, 2000);
    } else {
        window.FileTagManager?.showToast('云端同步已关闭', 'info');
    }
}

function handleAppLockChange(enabled) {
    if (enabled) {
        // 模拟设置应用锁
        const hasFingerprint = confirm('是否使用指纹解锁？\n\n点击"确定"使用指纹，点击"取消"使用密码。');
        if (hasFingerprint) {
            window.FileTagManager?.showToast('指纹解锁已启用', 'success');
        } else {
            const password = prompt('请设置6位数字密码：');
            if (password && password.length === 6 && /^\d+$/.test(password)) {
                window.FileTagManager?.showToast('密码解锁已启用', 'success');
            } else {
                // 重置开关状态
                document.getElementById('appLock').checked = false;
                localStorage.setItem('appLock', 'false');
                window.FileTagManager?.showToast('密码格式错误，请重新设置', 'error');
            }
        }
    } else {
        window.FileTagManager?.showToast('应用锁已关闭', 'info');
    }
}

function handleActionButton(event) {
    const button = event.currentTarget;
    const settingItem = button.closest('.setting-item');
    const settingName = settingItem.querySelector('.setting-name')?.textContent;
    
    // 添加点击动画
    button.style.transform = 'scale(0.95)';
    setTimeout(() => {
        button.style.transform = '';
    }, 150);
    
    // 根据设置名称执行相应操作
    switch (settingName) {
        case '导出标签':
            handleExportTags();
            break;
        case '导入标签':
            handleImportTags();
            break;
        case '立即备份':
            handleBackupData();
            break;
        case '选择备份':
            handleRestoreData();
            break;
        case '清除':
            handleClearCache();
            break;
        case '检查更新':
            handleCheckUpdate();
            break;
        case '查看':
            handleViewHelp();
            break;
        case '发送反馈':
            handleSendFeedback();
            break;
        default:
            window.FileTagManager?.showToast(`执行操作: ${settingName}`, 'info');
    }
}

function handleExportTags() {
    // 模拟导出标签数据
    const tagsData = [
        { name: '工作', color: 'blue', fileCount: 15 },
        { name: '重要', color: 'orange', fileCount: 8 },
        { name: '个人', color: 'purple', fileCount: 20 }
    ];
    
    const dataStr = JSON.stringify(tagsData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'tags-export.json';
    link.click();
    
    window.FileTagManager?.showToast('标签数据已导出', 'success');
}

function handleImportTags() {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json';
    fileInput.style.display = 'none';
    
    fileInput.addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const tags = JSON.parse(e.target.result);
                    window.FileTagManager?.showToast(`成功导入 ${tags.length} 个标签`, 'success');
                } catch (error) {
                    window.FileTagManager?.showToast('文件格式错误', 'error');
                }
            };
            reader.readAsText(file);
        }
    });
    
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
}

function handleBackupData() {
    const button = event.currentTarget;
    const originalText = button.textContent;
    
    button.textContent = '备份中...';
    button.disabled = true;
    
    // 模拟备份过程
    setTimeout(() => {
        const backupData = {
            settings: localStorage,
            tags: [],
            files: [],
            timestamp: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        button.textContent = originalText;
        button.disabled = false;
        
        window.FileTagManager?.showToast('数据备份完成', 'success');
    }, 2000);
}

function handleRestoreData() {
    const confirmed = confirm('恢复数据将覆盖当前所有设置和标签，确定继续吗？');
    
    if (confirmed) {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';
        fileInput.style.display = 'none';
        
        fileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const backupData = JSON.parse(e.target.result);
                        // 这里可以实现数据恢复逻辑
                        window.FileTagManager?.showToast('数据恢复完成，请重启应用', 'success');
                    } catch (error) {
                        window.FileTagManager?.showToast('备份文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        });
        
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    }
}

function handleClearCache() {
    const confirmed = confirm('确定要清除应用缓存吗？这将删除临时文件和缓存数据。');
    
    if (confirmed) {
        // 模拟清除缓存
        window.FileTagManager?.showToast('正在清除缓存...', 'info');
        
        setTimeout(() => {
            // 清除一些localStorage中的缓存数据
            const keysToKeep = ['theme', 'language', 'userName'];
            const allKeys = Object.keys(localStorage);
            
            allKeys.forEach(key => {
                if (!keysToKeep.includes(key)) {
                    localStorage.removeItem(key);
                }
            });
            
            window.FileTagManager?.showToast('缓存清除完成', 'success');
        }, 1500);
    }
}

function handleCheckUpdate() {
    const button = event.currentTarget;
    const originalText = button.textContent;
    
    button.textContent = '检查中...';
    button.disabled = true;
    
    // 模拟检查更新
    setTimeout(() => {
        const hasUpdate = Math.random() > 0.7; // 30%概率有更新
        
        if (hasUpdate) {
            const updateConfirmed = confirm('发现新版本 v1.1.0，是否立即更新？\n\n更新内容：\n- 新增批量标签功能\n- 优化搜索性能\n- 修复已知问题');
            if (updateConfirmed) {
                window.FileTagManager?.showToast('开始下载更新...', 'info');
            }
        } else {
            window.FileTagManager?.showToast('当前已是最新版本', 'info');
        }
        
        button.textContent = originalText;
        button.disabled = false;
    }, 2000);
}

function handleViewHelp() {
    // 模拟打开帮助页面
    window.FileTagManager?.showToast('正在打开帮助页面...', 'info');
    
    // 实际应用中可以打开帮助页面或显示帮助对话框
    setTimeout(() => {
        alert('帮助内容：\n\n1. 如何添加标签？\n   点击文件右侧的菜单按钮，选择"编辑标签"\n\n2. 如何筛选文件？\n   使用筛选页面的各种筛选条件\n\n3. 如何备份数据？\n   在设置页面点击"立即备份"按钮');
    }, 500);
}

function handleSendFeedback() {
    const feedback = prompt('请输入您的反馈和建议：');
    
    if (feedback && feedback.trim()) {
        window.FileTagManager?.showToast('正在发送反馈...', 'info');
        
        // 模拟发送反馈
        setTimeout(() => {
            window.FileTagManager?.showToast('反馈已发送，感谢您的建议！', 'success');
        }, 1500);
    }
}

function initializeStorageInfo() {
    // 模拟存储使用情况
    const totalStorage = 15 * 1024 * 1024 * 1024; // 15GB
    const usedStorage = 2.3 * 1024 * 1024 * 1024; // 2.3GB
    const usagePercentage = (usedStorage / totalStorage) * 100;
    
    // 更新存储条
    const storageUsed = document.querySelector('.storage-used');
    if (storageUsed) {
        storageUsed.style.width = `${usagePercentage}%`;
    }
    
    // 更新存储信息文本
    const storageInfo = document.querySelector('.storage-info');
    if (storageInfo) {
        storageInfo.textContent = `已使用 ${(usedStorage / (1024 * 1024 * 1024)).toFixed(1)} GB / ${totalStorage / (1024 * 1024 * 1024)} GB`;
    }
}
