// 文件详情页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeFileDetailPage();
});

function initializeFileDetailPage() {
    loadFileDetails();
    bindFileDetailEvents();
    initializeTagManagement();
}

function loadFileDetails() {
    // 从URL参数获取文件信息
    const urlParams = new URLSearchParams(window.location.search);
    const fileId = urlParams.get('id');
    const fileName = urlParams.get('name');
    
    if (fileName) {
        // 更新页面标题
        const fileTitle = document.querySelector('.file-title');
        if (fileTitle) {
            fileTitle.textContent = decodeURIComponent(fileName);
        }
        
        // 更新应用栏标题
        const appTitle = document.querySelector('.app-title');
        if (appTitle) {
            appTitle.textContent = '文件详情';
        }
    }
    
    // 模拟加载文件详细信息
    loadFileMetadata(fileId);
}

function loadFileMetadata(fileId) {
    // 模拟文件元数据
    const metadata = {
        size: '2.3 MB',
        location: '/Documents/Work',
        modified: '2024年1月15日 14:30',
        creator: '张三',
        type: 'DOCX'
    };
    
    // 更新元数据显示
    const metadataValues = document.querySelectorAll('.metadata-value');
    if (metadataValues.length >= 4) {
        metadataValues[0].textContent = metadata.location;
        metadataValues[1].textContent = metadata.size;
        metadataValues[2].textContent = metadata.modified;
        metadataValues[3].textContent = metadata.creator;
    }
    
    // 更新文件类型徽章
    const typeBadge = document.querySelector('.file-type-badge');
    if (typeBadge) {
        typeBadge.textContent = metadata.type;
    }
}

function bindFileDetailEvents() {
    // 添加标签按钮
    const addTagButton = document.getElementById('addTagButton');
    if (addTagButton) {
        addTagButton.addEventListener('click', toggleAddTagPanel);
    }
    
    // 移除标签按钮
    const removeTagButtons = document.querySelectorAll('.remove-tag');
    removeTagButtons.forEach(button => {
        button.addEventListener('click', handleRemoveTag);
    });
    
    // 文件操作按钮
    const actionItems = document.querySelectorAll('.action-item');
    actionItems.forEach(item => {
        item.addEventListener('click', handleFileAction);
    });
    
    // 相关文件点击
    const relatedItems = document.querySelectorAll('.related-item');
    relatedItems.forEach(item => {
        item.addEventListener('click', handleRelatedFileClick);
    });
    
    // 标签搜索
    const tagSearchInput = document.getElementById('tagSearchInput');
    if (tagSearchInput) {
        tagSearchInput.addEventListener('input', handleTagSearch);
    }
    
    // 建议标签点击
    const suggestionTags = document.querySelectorAll('.suggestion-tag');
    suggestionTags.forEach(tag => {
        tag.addEventListener('click', handleSuggestionTagClick);
    });
    
    // 所有标签选项点击
    const tagOptions = document.querySelectorAll('.tag-option');
    tagOptions.forEach(option => {
        option.addEventListener('click', handleTagOptionClick);
    });
}

function initializeTagManagement() {
    // 初始化标签面板状态
    const addTagPanel = document.getElementById('addTagPanel');
    if (addTagPanel) {
        addTagPanel.classList.remove('active');
    }
}

function toggleAddTagPanel() {
    const addTagPanel = document.getElementById('addTagPanel');
    const addTagButton = document.getElementById('addTagButton');
    
    if (addTagPanel && addTagButton) {
        const isActive = addTagPanel.classList.contains('active');
        
        if (isActive) {
            addTagPanel.classList.remove('active');
            addTagButton.querySelector('.material-icons').textContent = 'add';
        } else {
            addTagPanel.classList.add('active');
            addTagButton.querySelector('.material-icons').textContent = 'close';
            
            // 聚焦搜索框
            const searchInput = document.getElementById('tagSearchInput');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 300);
            }
        }
    }
}

function handleRemoveTag(event) {
    event.stopPropagation();
    
    const tagItem = event.currentTarget.closest('.tag-item');
    const tagName = tagItem.querySelector('.tag-name')?.textContent;
    
    // 添加移除动画
    tagItem.style.transition = 'all 0.3s ease';
    tagItem.style.opacity = '0';
    tagItem.style.transform = 'translateX(-20px)';
    
    setTimeout(() => {
        tagItem.remove();
        window.FileTagManager?.showToast(`已移除标签 "${tagName}"`, 'success');
    }, 300);
}

function handleFileAction(event) {
    const actionItem = event.currentTarget;
    const actionLabel = actionItem.querySelector('.action-label')?.textContent;
    const isDanger = actionItem.classList.contains('danger');
    
    // 添加点击动画
    actionItem.style.transform = 'scale(0.95)';
    setTimeout(() => {
        actionItem.style.transform = '';
    }, 150);
    
    // 处理不同的操作
    switch (actionLabel) {
        case '打开':
            handleOpenFile();
            break;
        case '编辑':
            handleEditFile();
            break;
        case '分享':
            handleShareFile();
            break;
        case '复制':
            handleCopyFile();
            break;
        case '移动':
            handleMoveFile();
            break;
        case '收藏':
            handleFavoriteFile();
            break;
        case '下载':
            handleDownloadFile();
            break;
        case '删除':
            handleDeleteFile();
            break;
        default:
            window.FileTagManager?.showToast(`执行操作: ${actionLabel}`, 'info');
    }
}

function handleOpenFile() {
    window.FileTagManager?.showToast('正在打开文件...', 'info');
    // 模拟打开文件
    setTimeout(() => {
        window.FileTagManager?.showToast('文件已在默认应用中打开', 'success');
    }, 1000);
}

function handleEditFile() {
    window.FileTagManager?.showToast('正在启动编辑器...', 'info');
}

function handleShareFile() {
    if (navigator.share) {
        navigator.share({
            title: document.querySelector('.file-title')?.textContent,
            text: '分享文件',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            window.FileTagManager?.showToast('链接已复制到剪贴板', 'success');
        });
    }
}

function handleCopyFile() {
    window.FileTagManager?.showToast('文件已复制', 'success');
}

function handleMoveFile() {
    const newLocation = prompt('请输入新位置：', '/Documents/');
    if (newLocation) {
        window.FileTagManager?.showToast(`文件已移动到 ${newLocation}`, 'success');
        // 更新位置显示
        const locationElement = document.querySelector('.metadata-value');
        if (locationElement) {
            locationElement.textContent = newLocation;
        }
    }
}

function handleFavoriteFile() {
    const actionItem = event.currentTarget;
    const icon = actionItem.querySelector('.material-icons');
    const label = actionItem.querySelector('.action-label');
    
    if (icon.textContent === 'star') {
        icon.textContent = 'star_border';
        label.textContent = '取消收藏';
        window.FileTagManager?.showToast('已添加到收藏', 'success');
    } else {
        icon.textContent = 'star';
        label.textContent = '收藏';
        window.FileTagManager?.showToast('已从收藏中移除', 'success');
    }
}

function handleDownloadFile() {
    window.FileTagManager?.showToast('开始下载文件...', 'info');
    // 模拟下载进度
    let progress = 0;
    const interval = setInterval(() => {
        progress += 20;
        if (progress <= 100) {
            window.FileTagManager?.showToast(`下载进度: ${progress}%`, 'info');
        } else {
            clearInterval(interval);
            window.FileTagManager?.showToast('文件下载完成', 'success');
        }
    }, 500);
}

function handleDeleteFile() {
    const fileName = document.querySelector('.file-title')?.textContent;
    const confirmed = confirm(`确定要删除文件 "${fileName}" 吗？\n\n此操作无法撤销。`);
    
    if (confirmed) {
        window.FileTagManager?.showToast('文件已删除', 'success');
        setTimeout(() => {
            window.history.back();
        }, 1500);
    }
}

function handleRelatedFileClick(event) {
    const relatedItem = event.currentTarget;
    const fileName = relatedItem.querySelector('.file-name')?.textContent;
    
    // 添加点击动画
    relatedItem.style.transform = 'scale(0.98)';
    setTimeout(() => {
        relatedItem.style.transform = '';
    }, 150);
    
    // 跳转到相关文件
    setTimeout(() => {
        window.FileTagManager?.showToast(`正在打开 "${fileName}"`, 'info');
        // 这里可以跳转到相关文件的详情页
    }, 150);
}

function handleTagSearch(event) {
    const query = event.target.value.toLowerCase();
    const tagOptions = document.querySelectorAll('.tag-option');
    const suggestionTags = document.querySelectorAll('.suggestion-tag');
    
    // 筛选建议标签
    suggestionTags.forEach(tag => {
        const tagName = tag.textContent.toLowerCase();
        tag.style.display = tagName.includes(query) || query === '' ? 'flex' : 'none';
    });
    
    // 筛选所有标签
    tagOptions.forEach(option => {
        const tagName = option.querySelector('.tag-name')?.textContent.toLowerCase();
        option.style.display = tagName?.includes(query) || query === '' ? 'flex' : 'none';
    });
}

function handleSuggestionTagClick(event) {
    const suggestionTag = event.currentTarget;
    const tagName = suggestionTag.textContent.trim();
    
    addTagToFile(tagName, suggestionTag.dataset.tag);
    
    // 添加点击动画
    suggestionTag.style.transform = 'scale(0.95)';
    setTimeout(() => {
        suggestionTag.style.transform = '';
    }, 150);
}

function handleTagOptionClick(event) {
    const tagOption = event.currentTarget;
    const tagName = tagOption.querySelector('.tag-name')?.textContent;
    const tagColor = tagOption.dataset.tag;
    
    addTagToFile(tagName, tagColor);
    
    // 添加点击动画
    tagOption.style.transform = 'scale(0.98)';
    setTimeout(() => {
        tagOption.style.transform = '';
    }, 150);
}

function addTagToFile(tagName, tagColor) {
    // 检查标签是否已存在
    const existingTags = document.querySelectorAll('.tag-item');
    const tagExists = Array.from(existingTags).some(item => {
        return item.querySelector('.tag-name')?.textContent === tagName;
    });
    
    if (tagExists) {
        window.FileTagManager?.showToast('标签已存在', 'warning');
        return;
    }
    
    // 创建新标签元素
    const newTagItem = document.createElement('div');
    newTagItem.className = 'tag-item';
    newTagItem.dataset.tag = tagColor;
    
    newTagItem.innerHTML = `
        <span class="tag-color ${tagColor}"></span>
        <span class="tag-name">${tagName}</span>
        <button class="icon-button remove-tag">
            <span class="material-icons">close</span>
        </button>
    `;
    
    // 添加到当前标签列表
    const currentTags = document.querySelector('.current-tags');
    if (currentTags) {
        currentTags.appendChild(newTagItem);
        
        // 添加创建动画
        newTagItem.style.opacity = '0';
        newTagItem.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            newTagItem.style.transition = 'all 0.3s ease';
            newTagItem.style.opacity = '1';
            newTagItem.style.transform = 'translateX(0)';
        }, 100);
        
        // 绑定移除事件
        newTagItem.querySelector('.remove-tag').addEventListener('click', handleRemoveTag);
        
        window.FileTagManager?.showToast(`已添加标签 "${tagName}"`, 'success');
    }
}
