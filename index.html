<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件标签管理器</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- 顶部应用栏 -->
    <header class="app-bar">
        <div class="app-bar-content">
            <button class="icon-button menu-button" id="menuButton">
                <span class="material-icons">menu</span>
            </button>
            <h1 class="app-title">我的文件</h1>
            <button class="icon-button search-button" id="searchButton">
                <span class="material-icons">search</span>
            </button>
            <button class="icon-button more-button">
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </header>

    <!-- 搜索栏 -->
    <div class="search-bar" id="searchBar">
        <div class="search-input-container">
            <span class="material-icons search-icon">search</span>
            <input type="text" placeholder="搜索文件..." class="search-input" id="searchInput">
            <button class="icon-button close-search" id="closeSearch">
                <span class="material-icons">close</span>
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 快速标签筛选 -->
        <section class="quick-filters">
            <div class="filter-chips">
                <div class="chip active" data-filter="all">
                    <span class="material-icons">folder</span>
                    全部文件
                </div>
                <div class="chip" data-filter="recent">
                    <span class="material-icons">schedule</span>
                    最近使用
                </div>
                <div class="chip" data-filter="important">
                    <span class="material-icons">star</span>
                    重要
                </div>
                <div class="chip" data-filter="work">
                    <span class="material-icons">work</span>
                    工作
                </div>
            </div>
        </section>

        <!-- 文件列表 -->
        <section class="file-list">
            <div class="file-item" data-file-id="1">
                <div class="file-icon">
                    <span class="material-icons">description</span>
                </div>
                <div class="file-info">
                    <h3 class="file-name">项目计划书.docx</h3>
                    <p class="file-details">2.3 MB • 2024年1月15日</p>
                    <div class="file-tags">
                        <span class="tag work">工作</span>
                        <span class="tag important">重要</span>
                    </div>
                </div>
                <button class="icon-button file-menu">
                    <span class="material-icons">more_vert</span>
                </button>
            </div>

            <div class="file-item" data-file-id="2">
                <div class="file-icon">
                    <span class="material-icons">image</span>
                </div>
                <div class="file-info">
                    <h3 class="file-name">度假照片.jpg</h3>
                    <p class="file-details">5.7 MB • 2024年1月10日</p>
                    <div class="file-tags">
                        <span class="tag personal">个人</span>
                        <span class="tag photos">照片</span>
                    </div>
                </div>
                <button class="icon-button file-menu">
                    <span class="material-icons">more_vert</span>
                </button>
            </div>

            <div class="file-item" data-file-id="3">
                <div class="file-icon">
                    <span class="material-icons">picture_as_pdf</span>
                </div>
                <div class="file-info">
                    <h3 class="file-name">用户手册.pdf</h3>
                    <p class="file-details">1.2 MB • 2024年1月8日</p>
                    <div class="file-tags">
                        <span class="tag reference">参考</span>
                    </div>
                </div>
                <button class="icon-button file-menu">
                    <span class="material-icons">more_vert</span>
                </button>
            </div>

            <div class="file-item" data-file-id="4">
                <div class="file-icon">
                    <span class="material-icons">music_note</span>
                </div>
                <div class="file-info">
                    <h3 class="file-name">我的音乐.mp3</h3>
                    <p class="file-details">4.1 MB • 2024年1月5日</p>
                    <div class="file-tags">
                        <span class="tag music">音乐</span>
                        <span class="tag favorite">收藏</span>
                    </div>
                </div>
                <button class="icon-button file-menu">
                    <span class="material-icons">more_vert</span>
                </button>
            </div>
        </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-navigation">
        <a href="index.html" class="nav-item active">
            <span class="material-icons">folder</span>
            <span class="nav-label">文件</span>
        </a>
        <a href="tags.html" class="nav-item">
            <span class="material-icons">label</span>
            <span class="nav-label">标签</span>
        </a>
        <a href="filter.html" class="nav-item">
            <span class="material-icons">filter_list</span>
            <span class="nav-label">筛选</span>
        </a>
        <a href="settings.html" class="nav-item">
            <span class="material-icons">settings</span>
            <span class="nav-label">设置</span>
        </a>
    </nav>

    <!-- 浮动操作按钮 -->
    <button class="fab" id="addButton">
        <span class="material-icons">add</span>
    </button>

    <script src="js/main.js"></script>
</body>
</html>
