<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签管理 - 文件标签管理器</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/tags.css">
</head>
<body>
    <!-- 顶部应用栏 -->
    <header class="app-bar">
        <div class="app-bar-content">
            <button class="icon-button back-button" onclick="history.back()">
                <span class="material-icons">arrow_back</span>
            </button>
            <h1 class="app-title">标签管理</h1>
            <button class="icon-button search-button" id="searchButton">
                <span class="material-icons">search</span>
            </button>
            <button class="icon-button more-button">
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </header>

    <!-- 搜索栏 -->
    <div class="search-bar" id="searchBar">
        <div class="search-input-container">
            <span class="material-icons search-icon">search</span>
            <input type="text" placeholder="搜索标签..." class="search-input" id="searchInput">
            <button class="icon-button close-search" id="closeSearch">
                <span class="material-icons">close</span>
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 标签统计 -->
        <section class="tag-stats">
            <div class="stats-card">
                <div class="stat-item">
                    <span class="stat-number">12</span>
                    <span class="stat-label">总标签数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">45</span>
                    <span class="stat-label">已标记文件</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span class="stat-label">未使用标签</span>
                </div>
            </div>
        </section>

        <!-- 快速操作 -->
        <section class="quick-actions">
            <button class="action-button primary" id="createTagButton">
                <span class="material-icons">add</span>
                创建新标签
            </button>
            <button class="action-button secondary" id="importTagsButton">
                <span class="material-icons">file_upload</span>
                导入标签
            </button>
        </section>

        <!-- 标签分类 -->
        <section class="tag-categories">
            <div class="category-section">
                <h2 class="category-title">
                    <span class="material-icons">work</span>
                    工作相关
                    <span class="category-count">4</span>
                </h2>
                <div class="tag-grid">
                    <div class="tag-card" data-tag-id="work">
                        <div class="tag-header">
                            <div class="tag-color work"></div>
                            <h3 class="tag-name">工作</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">15 个文件</span>
                            <span class="last-used">最近使用：今天</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="important">
                        <div class="tag-header">
                            <div class="tag-color important"></div>
                            <h3 class="tag-name">重要</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">8 个文件</span>
                            <span class="last-used">最近使用：昨天</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="project">
                        <div class="tag-header">
                            <div class="tag-color project"></div>
                            <h3 class="tag-name">项目</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">12 个文件</span>
                            <span class="last-used">最近使用：3天前</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="meeting">
                        <div class="tag-header">
                            <div class="tag-color meeting"></div>
                            <h3 class="tag-name">会议</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">6 个文件</span>
                            <span class="last-used">最近使用：1周前</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="category-section">
                <h2 class="category-title">
                    <span class="material-icons">person</span>
                    个人生活
                    <span class="category-count">5</span>
                </h2>
                <div class="tag-grid">
                    <div class="tag-card" data-tag-id="personal">
                        <div class="tag-header">
                            <div class="tag-color personal"></div>
                            <h3 class="tag-name">个人</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">20 个文件</span>
                            <span class="last-used">最近使用：今天</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="photos">
                        <div class="tag-header">
                            <div class="tag-color photos"></div>
                            <h3 class="tag-name">照片</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">35 个文件</span>
                            <span class="last-used">最近使用：2天前</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="music">
                        <div class="tag-header">
                            <div class="tag-color music"></div>
                            <h3 class="tag-name">音乐</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">18 个文件</span>
                            <span class="last-used">最近使用：5天前</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="favorite">
                        <div class="tag-header">
                            <div class="tag-color favorite"></div>
                            <h3 class="tag-name">收藏</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">9 个文件</span>
                            <span class="last-used">最近使用：1周前</span>
                        </div>
                    </div>

                    <div class="tag-card unused" data-tag-id="travel">
                        <div class="tag-header">
                            <div class="tag-color travel"></div>
                            <h3 class="tag-name">旅行</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">0 个文件</span>
                            <span class="last-used unused-label">未使用</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="category-section">
                <h2 class="category-title">
                    <span class="material-icons">school</span>
                    学习资料
                    <span class="category-count">3</span>
                </h2>
                <div class="tag-grid">
                    <div class="tag-card" data-tag-id="reference">
                        <div class="tag-header">
                            <div class="tag-color reference"></div>
                            <h3 class="tag-name">参考</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">7 个文件</span>
                            <span class="last-used">最近使用：3天前</span>
                        </div>
                    </div>

                    <div class="tag-card" data-tag-id="tutorial">
                        <div class="tag-header">
                            <div class="tag-color tutorial"></div>
                            <h3 class="tag-name">教程</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">4 个文件</span>
                            <span class="last-used">最近使用：1周前</span>
                        </div>
                    </div>

                    <div class="tag-card unused" data-tag-id="notes">
                        <div class="tag-header">
                            <div class="tag-color notes"></div>
                            <h3 class="tag-name">笔记</h3>
                            <button class="icon-button tag-menu">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="tag-info">
                            <span class="file-count">0 个文件</span>
                            <span class="last-used unused-label">未使用</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-navigation">
        <a href="index.html" class="nav-item">
            <span class="material-icons">folder</span>
            <span class="nav-label">文件</span>
        </a>
        <a href="tags.html" class="nav-item active">
            <span class="material-icons">label</span>
            <span class="nav-label">标签</span>
        </a>
        <a href="filter.html" class="nav-item">
            <span class="material-icons">filter_list</span>
            <span class="nav-label">筛选</span>
        </a>
        <a href="settings.html" class="nav-item">
            <span class="material-icons">settings</span>
            <span class="nav-label">设置</span>
        </a>
    </nav>

    <!-- 浮动操作按钮 -->
    <button class="fab" id="addTagFab">
        <span class="material-icons">add</span>
    </button>

    <script src="js/main.js"></script>
    <script src="js/tags.js"></script>
</body>
</html>
