<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签筛选 - 文件标签管理器</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/filter.css">
</head>
<body>
    <!-- 顶部应用栏 -->
    <header class="app-bar">
        <div class="app-bar-content">
            <button class="icon-button menu-button" id="menuButton">
                <span class="material-icons">menu</span>
            </button>
            <h1 class="app-title">标签筛选</h1>
            <button class="icon-button clear-button" id="clearFilters">
                <span class="material-icons">clear_all</span>
            </button>
            <button class="icon-button more-button">
                <span class="material-icons">more_vert</span>
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 筛选控制面板 -->
        <section class="filter-panel">
            <div class="filter-header">
                <h2 class="filter-title">筛选条件</h2>
                <div class="filter-stats">
                    <span class="result-count">找到 <strong>23</strong> 个文件</span>
                </div>
            </div>

            <!-- 快速筛选 -->
            <div class="quick-filters">
                <h3 class="filter-section-title">快速筛选</h3>
                <div class="quick-filter-chips">
                    <button class="filter-chip" data-filter="recent">
                        <span class="material-icons">schedule</span>
                        最近使用
                    </button>
                    <button class="filter-chip" data-filter="starred">
                        <span class="material-icons">star</span>
                        已收藏
                    </button>
                    <button class="filter-chip" data-filter="large">
                        <span class="material-icons">storage</span>
                        大文件
                    </button>
                    <button class="filter-chip" data-filter="shared">
                        <span class="material-icons">share</span>
                        已分享
                    </button>
                </div>
            </div>

            <!-- 标签筛选 -->
            <div class="tag-filters">
                <h3 class="filter-section-title">按标签筛选</h3>
                <div class="filter-mode-toggle">
                    <button class="mode-button active" data-mode="any">任意标签</button>
                    <button class="mode-button" data-mode="all">所有标签</button>
                </div>
                <div class="tag-filter-grid">
                    <div class="tag-filter-item" data-tag="work">
                        <input type="checkbox" id="tag-work" class="tag-checkbox">
                        <label for="tag-work" class="tag-filter-label">
                            <span class="tag-color work"></span>
                            <span class="tag-name">工作</span>
                            <span class="file-count">15</span>
                        </label>
                    </div>
                    <div class="tag-filter-item" data-tag="important">
                        <input type="checkbox" id="tag-important" class="tag-checkbox" checked>
                        <label for="tag-important" class="tag-filter-label">
                            <span class="tag-color important"></span>
                            <span class="tag-name">重要</span>
                            <span class="file-count">8</span>
                        </label>
                    </div>
                    <div class="tag-filter-item" data-tag="personal">
                        <input type="checkbox" id="tag-personal" class="tag-checkbox">
                        <label for="tag-personal" class="tag-filter-label">
                            <span class="tag-color personal"></span>
                            <span class="tag-name">个人</span>
                            <span class="file-count">20</span>
                        </label>
                    </div>
                    <div class="tag-filter-item" data-tag="photos">
                        <input type="checkbox" id="tag-photos" class="tag-checkbox" checked>
                        <label for="tag-photos" class="tag-filter-label">
                            <span class="tag-color photos"></span>
                            <span class="tag-name">照片</span>
                            <span class="file-count">35</span>
                        </label>
                    </div>
                    <div class="tag-filter-item" data-tag="music">
                        <input type="checkbox" id="tag-music" class="tag-checkbox">
                        <label for="tag-music" class="tag-filter-label">
                            <span class="tag-color music"></span>
                            <span class="tag-name">音乐</span>
                            <span class="file-count">18</span>
                        </label>
                    </div>
                    <div class="tag-filter-item" data-tag="reference">
                        <input type="checkbox" id="tag-reference" class="tag-checkbox">
                        <label for="tag-reference" class="tag-filter-label">
                            <span class="tag-color reference"></span>
                            <span class="tag-name">参考</span>
                            <span class="file-count">7</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 文件类型筛选 -->
            <div class="type-filters">
                <h3 class="filter-section-title">文件类型</h3>
                <div class="type-filter-grid">
                    <div class="type-filter-item">
                        <input type="checkbox" id="type-document" class="type-checkbox">
                        <label for="type-document" class="type-filter-label">
                            <span class="material-icons">description</span>
                            <span class="type-name">文档</span>
                            <span class="file-count">12</span>
                        </label>
                    </div>
                    <div class="type-filter-item">
                        <input type="checkbox" id="type-image" class="type-checkbox" checked>
                        <label for="type-image" class="type-filter-label">
                            <span class="material-icons">image</span>
                            <span class="type-name">图片</span>
                            <span class="file-count">45</span>
                        </label>
                    </div>
                    <div class="type-filter-item">
                        <input type="checkbox" id="type-video" class="type-checkbox">
                        <label for="type-video" class="type-filter-label">
                            <span class="material-icons">movie</span>
                            <span class="type-name">视频</span>
                            <span class="file-count">8</span>
                        </label>
                    </div>
                    <div class="type-filter-item">
                        <input type="checkbox" id="type-audio" class="type-checkbox">
                        <label for="type-audio" class="type-filter-label">
                            <span class="material-icons">music_note</span>
                            <span class="type-name">音频</span>
                            <span class="file-count">23</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 时间范围筛选 -->
            <div class="time-filters">
                <h3 class="filter-section-title">时间范围</h3>
                <div class="time-filter-options">
                    <label class="time-option">
                        <input type="radio" name="timeRange" value="today">
                        <span class="radio-custom"></span>
                        今天
                    </label>
                    <label class="time-option">
                        <input type="radio" name="timeRange" value="week">
                        <span class="radio-custom"></span>
                        本周
                    </label>
                    <label class="time-option">
                        <input type="radio" name="timeRange" value="month" checked>
                        <span class="radio-custom"></span>
                        本月
                    </label>
                    <label class="time-option">
                        <input type="radio" name="timeRange" value="year">
                        <span class="radio-custom"></span>
                        今年
                    </label>
                    <label class="time-option">
                        <input type="radio" name="timeRange" value="custom">
                        <span class="radio-custom"></span>
                        自定义
                    </label>
                </div>
                <div class="custom-date-range" id="customDateRange">
                    <div class="date-input-group">
                        <label for="startDate">开始日期</label>
                        <input type="date" id="startDate" class="date-input">
                    </div>
                    <div class="date-input-group">
                        <label for="endDate">结束日期</label>
                        <input type="date" id="endDate" class="date-input">
                    </div>
                </div>
            </div>

            <!-- 文件大小筛选 -->
            <div class="size-filters">
                <h3 class="filter-section-title">文件大小</h3>
                <div class="size-range-slider">
                    <input type="range" id="sizeRange" min="0" max="100" value="50" class="range-slider">
                    <div class="range-labels">
                        <span>0 KB</span>
                        <span>100 MB+</span>
                    </div>
                </div>
                <div class="size-presets">
                    <button class="size-preset" data-size="small">小文件 (&lt;1MB)</button>
                    <button class="size-preset" data-size="medium">中等 (1-10MB)</button>
                    <button class="size-preset" data-size="large">大文件 (&gt;10MB)</button>
                </div>
            </div>
        </section>

        <!-- 筛选结果 -->
        <section class="filter-results">
            <div class="results-header">
                <div class="sort-controls">
                    <label for="sortBy">排序方式：</label>
                    <select id="sortBy" class="sort-select">
                        <option value="name">名称</option>
                        <option value="date" selected>修改时间</option>
                        <option value="size">文件大小</option>
                        <option value="type">文件类型</option>
                    </select>
                    <button class="icon-button sort-order" id="sortOrder" data-order="desc">
                        <span class="material-icons">arrow_downward</span>
                    </button>
                </div>
                <div class="view-controls">
                    <button class="icon-button view-toggle active" data-view="list">
                        <span class="material-icons">view_list</span>
                    </button>
                    <button class="icon-button view-toggle" data-view="grid">
                        <span class="material-icons">view_module</span>
                    </button>
                </div>
            </div>

            <div class="file-results">
                <div class="file-item" data-file-id="1">
                    <div class="file-icon">
                        <span class="material-icons">image</span>
                    </div>
                    <div class="file-info">
                        <h3 class="file-name">度假照片.jpg</h3>
                        <p class="file-details">5.7 MB • 2024年1月10日</p>
                        <div class="file-tags">
                            <span class="tag photos">照片</span>
                            <span class="tag important">重要</span>
                        </div>
                    </div>
                    <button class="icon-button file-menu">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>

                <div class="file-item" data-file-id="2">
                    <div class="file-icon">
                        <span class="material-icons">image</span>
                    </div>
                    <div class="file-info">
                        <h3 class="file-name">风景照.png</h3>
                        <p class="file-details">3.2 MB • 2024年1月8日</p>
                        <div class="file-tags">
                            <span class="tag photos">照片</span>
                        </div>
                    </div>
                    <button class="icon-button file-menu">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>

                <div class="file-item" data-file-id="3">
                    <div class="file-icon">
                        <span class="material-icons">image</span>
                    </div>
                    <div class="file-info">
                        <h3 class="file-name">重要文档截图.jpg</h3>
                        <p class="file-details">1.8 MB • 2024年1月5日</p>
                        <div class="file-tags">
                            <span class="tag photos">照片</span>
                            <span class="tag important">重要</span>
                        </div>
                    </div>
                    <button class="icon-button file-menu">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more">
                <button class="load-more-button">
                    <span class="material-icons">expand_more</span>
                    加载更多
                </button>
            </div>
        </section>
    </main>

    <!-- 底部导航栏 -->
    <nav class="bottom-navigation">
        <a href="index.html" class="nav-item">
            <span class="material-icons">folder</span>
            <span class="nav-label">文件</span>
        </a>
        <a href="tags.html" class="nav-item">
            <span class="material-icons">label</span>
            <span class="nav-label">标签</span>
        </a>
        <a href="filter.html" class="nav-item active">
            <span class="material-icons">filter_list</span>
            <span class="nav-label">筛选</span>
        </a>
        <a href="settings.html" class="nav-item">
            <span class="material-icons">settings</span>
            <span class="nav-label">设置</span>
        </a>
    </nav>

    <script src="js/main.js"></script>
    <script src="js/filter.js"></script>
</body>
</html>
