// 主要JavaScript文件 - 处理通用交互功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化应用
    initializeApp();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 设置主题
    initializeTheme();
});

// 应用初始化
function initializeApp() {
    console.log('文件标签管理器已启动');
    
    // 检查是否为移动设备
    if (isMobileDevice()) {
        document.body.classList.add('mobile');
    }
    
    // 设置视口高度
    setViewportHeight();
    
    // 初始化触摸事件
    initializeTouchEvents();
}

// 绑定事件监听器
function bindEventListeners() {
    // 搜索功能
    const searchButton = document.getElementById('searchButton');
    const searchBar = document.getElementById('searchBar');
    const closeSearch = document.getElementById('closeSearch');
    const searchInput = document.getElementById('searchInput');
    
    if (searchButton && searchBar) {
        searchButton.addEventListener('click', toggleSearch);
        closeSearch?.addEventListener('click', closeSearchBar);
        searchInput?.addEventListener('input', handleSearch);
    }
    
    // 文件项点击事件
    const fileItems = document.querySelectorAll('.file-item');
    fileItems.forEach(item => {
        item.addEventListener('click', handleFileClick);
    });
    
    // 筛选标签点击事件
    const filterChips = document.querySelectorAll('.chip');
    filterChips.forEach(chip => {
        chip.addEventListener('click', handleFilterClick);
    });
    
    // 浮动操作按钮
    const fab = document.querySelector('.fab');
    if (fab) {
        fab.addEventListener('click', handleFabClick);
    }
    
    // 文件菜单按钮
    const fileMenuButtons = document.querySelectorAll('.file-menu');
    fileMenuButtons.forEach(button => {
        button.addEventListener('click', handleFileMenuClick);
    });
    
    // 底部导航
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', handleNavClick);
    });
}

// 搜索功能
function toggleSearch() {
    const searchBar = document.getElementById('searchBar');
    const searchInput = document.getElementById('searchInput');
    
    if (searchBar.classList.contains('active')) {
        closeSearchBar();
    } else {
        searchBar.classList.add('active');
        setTimeout(() => {
            searchInput?.focus();
        }, 300);
    }
}

function closeSearchBar() {
    const searchBar = document.getElementById('searchBar');
    const searchInput = document.getElementById('searchInput');
    
    searchBar.classList.remove('active');
    if (searchInput) {
        searchInput.value = '';
    }
}

function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    const fileItems = document.querySelectorAll('.file-item');
    
    fileItems.forEach(item => {
        const fileName = item.querySelector('.file-name')?.textContent.toLowerCase();
        const tags = Array.from(item.querySelectorAll('.tag')).map(tag => tag.textContent.toLowerCase());
        
        const matches = fileName?.includes(query) || tags.some(tag => tag.includes(query));
        
        if (matches || query === '') {
            item.style.display = 'flex';
            item.style.animation = 'fadeIn 0.3s ease';
        } else {
            item.style.display = 'none';
        }
    });
}

// 文件项点击处理
function handleFileClick(event) {
    // 防止菜单按钮触发文件点击
    if (event.target.closest('.file-menu')) {
        return;
    }
    
    const fileId = event.currentTarget.dataset.fileId;
    const fileName = event.currentTarget.querySelector('.file-name')?.textContent;
    
    // 添加点击动画
    event.currentTarget.style.transform = 'scale(0.98)';
    setTimeout(() => {
        event.currentTarget.style.transform = '';
    }, 150);
    
    // 跳转到文件详情页
    setTimeout(() => {
        window.location.href = `file-detail.html?id=${fileId}&name=${encodeURIComponent(fileName)}`;
    }, 150);
}

// 筛选标签点击处理
function handleFilterClick(event) {
    const chip = event.currentTarget;
    const filter = chip.dataset.filter;
    
    // 移除其他活动状态
    document.querySelectorAll('.chip').forEach(c => c.classList.remove('active'));
    
    // 添加当前活动状态
    chip.classList.add('active');
    
    // 执行筛选
    filterFiles(filter);
    
    // 添加点击反馈
    chip.style.transform = 'scale(0.95)';
    setTimeout(() => {
        chip.style.transform = '';
    }, 150);
}

// 文件筛选逻辑
function filterFiles(filter) {
    const fileItems = document.querySelectorAll('.file-item');
    
    fileItems.forEach(item => {
        let shouldShow = true;
        
        switch (filter) {
            case 'all':
                shouldShow = true;
                break;
            case 'recent':
                // 模拟最近使用逻辑
                shouldShow = Math.random() > 0.5;
                break;
            case 'important':
                shouldShow = item.querySelector('.tag.important') !== null;
                break;
            case 'work':
                shouldShow = item.querySelector('.tag.work') !== null;
                break;
            default:
                shouldShow = true;
        }
        
        if (shouldShow) {
            item.style.display = 'flex';
            item.style.animation = 'fadeIn 0.3s ease';
        } else {
            item.style.display = 'none';
        }
    });
}

// 浮动操作按钮点击处理
function handleFabClick(event) {
    const fab = event.currentTarget;
    
    // 添加点击动画
    fab.style.transform = 'scale(0.9)';
    setTimeout(() => {
        fab.style.transform = '';
    }, 150);
    
    // 显示添加选项（这里可以扩展为显示菜单）
    showAddOptions();
}

function showAddOptions() {
    // 简单的提示，实际应用中可以显示底部菜单
    const options = ['添加文件', '创建标签', '导入文件'];
    const choice = prompt('选择操作：\n1. 添加文件\n2. 创建标签\n3. 导入文件');
    
    if (choice) {
        console.log(`选择了选项: ${options[parseInt(choice) - 1]}`);
    }
}

// 文件菜单点击处理
function handleFileMenuClick(event) {
    event.stopPropagation();
    
    const button = event.currentTarget;
    const fileItem = button.closest('.file-item');
    const fileName = fileItem.querySelector('.file-name')?.textContent;
    
    // 添加点击动画
    button.style.transform = 'scale(0.9)';
    setTimeout(() => {
        button.style.transform = '';
    }, 150);
    
    // 显示文件菜单
    showFileMenu(fileName);
}

function showFileMenu(fileName) {
    const actions = ['打开', '编辑标签', '分享', '重命名', '删除'];
    const choice = prompt(`文件: ${fileName}\n\n选择操作：\n1. 打开\n2. 编辑标签\n3. 分享\n4. 重命名\n5. 删除`);
    
    if (choice) {
        console.log(`对文件 "${fileName}" 执行操作: ${actions[parseInt(choice) - 1]}`);
    }
}

// 底部导航点击处理
function handleNavClick(event) {
    const navItem = event.currentTarget;
    const href = navItem.getAttribute('href');
    
    // 如果是当前页面，不执行跳转
    if (navItem.classList.contains('active')) {
        event.preventDefault();
        return;
    }
    
    // 添加点击动画
    navItem.style.transform = 'scale(0.95)';
    setTimeout(() => {
        navItem.style.transform = '';
    }, 150);
}

// 主题初始化
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme');
    const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && systemDark)) {
        document.body.classList.add('dark-theme');
    }
    
    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            if (e.matches) {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }
        }
    });
}

// 工具函数
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

function setViewportHeight() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    
    window.addEventListener('resize', () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    });
}

function initializeTouchEvents() {
    // 为移动设备优化触摸事件
    if (isMobileDevice()) {
        // 禁用双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 添加触摸反馈
        document.addEventListener('touchstart', function(event) {
            const target = event.target.closest('.file-item, .chip, .nav-item, .fab');
            if (target) {
                target.style.opacity = '0.7';
            }
        });
        
        document.addEventListener('touchend', function(event) {
            const target = event.target.closest('.file-item, .chip, .nav-item, .fab');
            if (target) {
                setTimeout(() => {
                    target.style.opacity = '';
                }, 150);
            }
        });
    }
}

// 通用动画函数
function animateElement(element, animation = 'pulse') {
    element.style.animation = `${animation} 0.3s ease`;
    setTimeout(() => {
        element.style.animation = '';
    }, 300);
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 导出全局函数供其他脚本使用
window.FileTagManager = {
    showToast,
    animateElement,
    filterFiles,
    isMobileDevice
};
