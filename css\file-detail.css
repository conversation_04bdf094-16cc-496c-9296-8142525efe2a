/* 文件详情页面专用样式 */

/* 文件预览区域 */
.file-preview {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    padding: 40px 20px;
    text-align: center;
    margin-bottom: 24px;
}

.preview-container {
    position: relative;
    display: inline-block;
}

.file-icon-large {
    width: 120px;
    height: 120px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    backdrop-filter: blur(10px);
}

.file-icon-large .material-icons {
    font-size: 64px;
    color: white;
}

.file-type-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 文件信息卡片 */
.file-info-card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-1);
}

.file-title {
    font-size: 24px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 20px;
    text-align: center;
}

.file-metadata {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.metadata-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.metadata-item .material-icons {
    font-size: 20px;
    color: var(--text-secondary);
    width: 24px;
}

.metadata-label {
    font-size: 14px;
    color: var(--text-secondary);
    min-width: 80px;
}

.metadata-value {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    flex: 1;
}

/* 标签管理区域 */
.tags-section {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-1);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

.section-title .material-icons {
    font-size: 20px;
    color: var(--primary-color);
}

.current-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.tag-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: var(--background-color);
    border-radius: 20px;
    border: 1px solid var(--divider-color);
}

.tag-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.tag-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.remove-tag {
    width: 20px;
    height: 20px;
    padding: 0;
}

.remove-tag .material-icons {
    font-size: 16px;
}

/* 添加标签面板 */
.add-tag-panel {
    display: none;
    border-top: 1px solid var(--divider-color);
    padding-top: 16px;
    margin-top: 16px;
}

.add-tag-panel.active {
    display: block;
}

.search-tags {
    margin-bottom: 20px;
}

.tag-search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--divider-color);
    border-radius: 8px;
    font-size: 16px;
    background-color: var(--background-color);
    color: var(--text-primary);
}

.tag-search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.suggestions-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.tag-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.suggestion-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.suggestion-tag:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tag-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tag-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
}

.tag-option:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tag-option .tag-name {
    flex: 1;
}

.file-count {
    font-size: 12px;
    color: var(--text-secondary);
}

.tag-option:hover .file-count {
    color: rgba(255, 255, 255, 0.8);
}

/* 文件操作网格 */
.file-actions {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-1);
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 8px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
}

.action-item:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
}

.action-item.danger:hover {
    background-color: var(--error-color);
    border-color: var(--error-color);
}

.action-item .material-icons {
    font-size: 24px;
}

.action-label {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

/* 相关文件和历史记录 */
.related-files,
.file-history {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-1);
}

.related-list,
.history-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.related-item,
.history-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background-color: var(--background-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.related-item:hover {
    background-color: var(--primary-light);
    color: white;
}

.related-item .file-icon,
.history-item .history-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-light);
    border-radius: 8px;
}

.related-item .file-icon .material-icons,
.history-item .history-icon .material-icons {
    font-size: 20px;
    color: white;
}

.related-item .file-info,
.history-item .history-info {
    flex: 1;
}

.related-item .file-name,
.history-item .history-action {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.related-item .file-details,
.history-item .history-time {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .action-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .file-preview {
        padding: 24px 16px;
    }
    
    .file-icon-large {
        width: 80px;
        height: 80px;
    }
    
    .file-icon-large .material-icons {
        font-size: 40px;
    }
    
    .file-info-card,
    .tags-section,
    .file-actions,
    .related-files,
    .file-history {
        padding: 16px;
    }
    
    .file-title {
        font-size: 20px;
    }
    
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .metadata-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .metadata-label {
        min-width: auto;
    }
}

/* 动画效果 */
@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-info-card,
.tags-section,
.file-actions,
.related-files,
.file-history {
    animation: slideInFromBottom 0.4s ease;
}

.file-info-card { animation-delay: 0.1s; }
.tags-section { animation-delay: 0.2s; }
.file-actions { animation-delay: 0.3s; }
.related-files { animation-delay: 0.4s; }
.file-history { animation-delay: 0.5s; }
