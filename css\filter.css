/* 标签筛选页面专用样式 */

/* 筛选面板 */
.filter-panel {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-1);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--divider-color);
}

.filter-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
}

.filter-stats {
    text-align: right;
}

.result-count {
    font-size: 14px;
    color: var(--text-secondary);
}

.result-count strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 筛选区域通用样式 */
.quick-filters,
.tag-filters,
.type-filters,
.time-filters,
.size-filters {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--divider-color);
}

.size-filters {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-section-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 快速筛选 */
.quick-filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-chip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
}

.filter-chip:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.filter-chip.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.filter-chip .material-icons {
    font-size: 18px;
}

/* 标签筛选 */
.filter-mode-toggle {
    display: flex;
    background-color: var(--background-color);
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 16px;
    border: 1px solid var(--divider-color);
}

.mode-button {
    flex: 1;
    padding: 8px 16px;
    border: none;
    background: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-secondary);
}

.mode-button.active {
    background-color: var(--primary-color);
    color: white;
}

.tag-filter-grid,
.type-filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
}

.tag-filter-item,
.type-filter-item {
    position: relative;
}

.tag-checkbox,
.type-checkbox {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.tag-filter-label,
.type-filter-label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background-color: var(--background-color);
    border: 2px solid var(--divider-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
}

.tag-filter-label:hover,
.type-filter-label:hover {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.1);
}

.tag-checkbox:checked + .tag-filter-label,
.type-checkbox:checked + .type-filter-label {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.1);
}

.tag-name,
.type-name {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.file-count {
    background-color: var(--divider-color);
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.tag-checkbox:checked + .tag-filter-label .file-count,
.type-checkbox:checked + .type-filter-label .file-count {
    background-color: var(--primary-color);
    color: white;
}

/* 时间筛选 */
.time-filter-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.time-option {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-primary);
}

.time-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--divider-color);
    border-radius: 50%;
    position: relative;
    transition: all 0.2s;
}

.time-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary-color);
}

.time-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.custom-date-range {
    display: none;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 16px;
    background-color: var(--background-color);
    border-radius: 8px;
    border: 1px solid var(--divider-color);
}

.custom-date-range.active {
    display: grid;
}

.date-input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.date-input-group label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.date-input {
    padding: 8px 12px;
    border: 1px solid var(--divider-color);
    border-radius: 4px;
    font-size: 14px;
    background-color: var(--surface-color);
    color: var(--text-primary);
}

.date-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 文件大小筛选 */
.size-range-slider {
    margin-bottom: 16px;
}

.range-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--divider-color);
    outline: none;
    -webkit-appearance: none;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

.range-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.size-presets {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.size-preset {
    padding: 6px 12px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
}

.size-preset:hover,
.size-preset.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 筛选结果 */
.filter-results {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 24px;
    box-shadow: var(--shadow-1);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--divider-color);
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.sort-controls label {
    font-size: 14px;
    color: var(--text-secondary);
}

.sort-select {
    padding: 6px 12px;
    border: 1px solid var(--divider-color);
    border-radius: 4px;
    background-color: var(--background-color);
    color: var(--text-primary);
    font-size: 14px;
}

.sort-order {
    transition: transform 0.2s;
}

.sort-order[data-order="asc"] {
    transform: rotate(180deg);
}

.view-controls {
    display: flex;
    gap: 4px;
}

.view-toggle {
    width: 40px;
    height: 40px;
}

.view-toggle.active {
    background-color: var(--primary-color);
    color: white;
}

.file-results {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.load-more {
    text-align: center;
    margin-top: 24px;
}

.load-more-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background-color: var(--background-color);
    border: 1px solid var(--divider-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
    margin: 0 auto;
}

.load-more-button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tag-filter-grid,
    .type-filter-grid {
        grid-template-columns: 1fr;
    }
    
    .results-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .sort-controls {
        justify-content: space-between;
    }
    
    .view-controls {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .filter-panel,
    .filter-results {
        padding: 16px;
    }
    
    .filter-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .quick-filter-chips {
        justify-content: center;
    }
    
    .custom-date-range {
        grid-template-columns: 1fr;
    }
    
    .size-presets {
        justify-content: center;
    }
}
