/* Material Design 基础样式 */
:root {
    --primary-color: #1976d2;
    --primary-dark: #1565c0;
    --primary-light: #42a5f5;
    --accent-color: #ff4081;
    --background-color: #fafafa;
    --surface-color: #ffffff;
    --error-color: #f44336;
    --text-primary: #212121;
    --text-secondary: #757575;
    --divider-color: #e0e0e0;
    --shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #121212;
        --surface-color: #1e1e1e;
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --divider-color: #333333;
    }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    overflow-x: hidden;
}

/* 应用栏样式 */
.app-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-2);
    z-index: 1000;
}

.app-bar-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 4px;
}

.app-title {
    flex: 1;
    font-size: 20px;
    font-weight: 500;
    margin-left: 16px;
}

.icon-button {
    width: 48px;
    height: 48px;
    border: none;
    background: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
    color: inherit;
}

.icon-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.icon-button:active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 搜索栏样式 */
.search-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: var(--surface-color);
    box-shadow: var(--shadow-2);
    z-index: 999;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.search-bar.active {
    transform: translateY(0);
}

.search-input-container {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
}

.search-icon {
    color: var(--text-secondary);
    margin-right: 16px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    background: none;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-secondary);
}

/* 主要内容区域 */
.main-content {
    margin-top: 56px;
    margin-bottom: 80px;
    padding: 16px;
}

/* 快速筛选标签 */
.quick-filters {
    margin-bottom: 24px;
}

.filter-chips {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 8px;
}

.chip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: var(--surface-color);
    border: 1px solid var(--divider-color);
    border-radius: 16px;
    font-size: 14px;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s;
}

.chip:hover {
    background-color: var(--primary-light);
    color: white;
}

.chip.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chip .material-icons {
    font-size: 18px;
}

/* 文件列表样式 */
.file-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: var(--shadow-1);
    cursor: pointer;
    transition: all 0.2s;
}

.file-item:hover {
    box-shadow: var(--shadow-2);
    transform: translateY(-1px);
}

.file-item:active {
    transform: translateY(0);
    box-shadow: var(--shadow-1);
}

.file-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-light);
    border-radius: 8px;
    margin-right: 16px;
}

.file-icon .material-icons {
    font-size: 24px;
    color: white;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
}

.file-details {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.file-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.tag.work { background-color: #e3f2fd; color: #1976d2; }
.tag.important { background-color: #fff3e0; color: #f57c00; }
.tag.personal { background-color: #f3e5f5; color: #7b1fa2; }
.tag.photos { background-color: #e8f5e8; color: #388e3c; }
.tag.reference { background-color: #fce4ec; color: #c2185b; }
.tag.music { background-color: #e1f5fe; color: #0277bd; }
.tag.favorite { background-color: #fff8e1; color: #f9a825; }

/* 底部导航栏 */
.bottom-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 64px;
    background-color: var(--surface-color);
    border-top: 1px solid var(--divider-color);
    display: flex;
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--text-secondary);
    transition: color 0.2s;
}

.nav-item.active {
    color: var(--primary-color);
}

.nav-item .material-icons {
    font-size: 24px;
    margin-bottom: 4px;
}

.nav-label {
    font-size: 12px;
    font-weight: 500;
}

/* 浮动操作按钮 */
.fab {
    position: fixed;
    bottom: 80px;
    right: 16px;
    width: 56px;
    height: 56px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-3);
    cursor: pointer;
    transition: all 0.2s;
    z-index: 999;
}

.fab:hover {
    transform: scale(1.1);
}

.fab:active {
    transform: scale(0.95);
}

.fab .material-icons {
    font-size: 24px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .main-content {
        padding: 12px;
    }
    
    .file-item {
        padding: 12px;
    }
    
    .file-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }
    
    .file-icon .material-icons {
        font-size: 20px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.file-item {
    animation: fadeIn 0.3s ease;
}

/* 可访问性 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
