// 筛选页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeFilterPage();
});

function initializeFilterPage() {
    bindFilterEvents();
    loadFilterFromURL();
    initializeFilterState();
}

function bindFilterEvents() {
    // 清除筛选按钮
    const clearFilters = document.getElementById('clearFilters');
    if (clearFilters) {
        clearFilters.addEventListener('click', handleClearFilters);
    }
    
    // 快速筛选标签
    const quickFilterChips = document.querySelectorAll('.filter-chip');
    quickFilterChips.forEach(chip => {
        chip.addEventListener('click', handleQuickFilterClick);
    });
    
    // 筛选模式切换
    const modeButtons = document.querySelectorAll('.mode-button');
    modeButtons.forEach(button => {
        button.addEventListener('click', handleFilterModeChange);
    });
    
    // 标签筛选复选框
    const tagCheckboxes = document.querySelectorAll('.tag-checkbox');
    tagCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleTagFilterChange);
    });
    
    // 文件类型筛选复选框
    const typeCheckboxes = document.querySelectorAll('.type-checkbox');
    typeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleTypeFilterChange);
    });
    
    // 时间范围筛选
    const timeRadios = document.querySelectorAll('input[name="timeRange"]');
    timeRadios.forEach(radio => {
        radio.addEventListener('change', handleTimeRangeChange);
    });
    
    // 自定义日期范围
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    if (startDate && endDate) {
        startDate.addEventListener('change', handleCustomDateChange);
        endDate.addEventListener('change', handleCustomDateChange);
    }
    
    // 文件大小滑块
    const sizeRange = document.getElementById('sizeRange');
    if (sizeRange) {
        sizeRange.addEventListener('input', handleSizeRangeChange);
    }
    
    // 大小预设按钮
    const sizePresets = document.querySelectorAll('.size-preset');
    sizePresets.forEach(preset => {
        preset.addEventListener('click', handleSizePresetClick);
    });
    
    // 排序控件
    const sortBy = document.getElementById('sortBy');
    const sortOrder = document.getElementById('sortOrder');
    if (sortBy) {
        sortBy.addEventListener('change', handleSortChange);
    }
    if (sortOrder) {
        sortOrder.addEventListener('click', handleSortOrderChange);
    }
    
    // 视图切换
    const viewToggles = document.querySelectorAll('.view-toggle');
    viewToggles.forEach(toggle => {
        toggle.addEventListener('click', handleViewToggle);
    });
    
    // 加载更多按钮
    const loadMoreButton = document.querySelector('.load-more-button');
    if (loadMoreButton) {
        loadMoreButton.addEventListener('click', handleLoadMore);
    }
}

function loadFilterFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const tag = urlParams.get('tag');
    
    if (tag) {
        // 如果URL中有标签参数，自动选中对应的标签
        const tagCheckbox = document.querySelector(`input[id="tag-${tag.toLowerCase()}"]`);
        if (tagCheckbox) {
            tagCheckbox.checked = true;
            applyFilters();
        }
    }
}

function initializeFilterState() {
    // 初始化筛选状态
    updateResultCount();
    
    // 设置默认日期范围
    const today = new Date();
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate && endDate) {
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        startDate.value = firstDayOfMonth.toISOString().split('T')[0];
        endDate.value = today.toISOString().split('T')[0];
    }
}

function handleClearFilters() {
    // 清除所有筛选条件
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.classList.remove('active');
    });
    
    document.querySelectorAll('.tag-checkbox, .type-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    document.querySelectorAll('input[name="timeRange"]').forEach(radio => {
        radio.checked = false;
    });
    
    document.querySelector('input[name="timeRange"][value="month"]').checked = true;
    
    const sizeRange = document.getElementById('sizeRange');
    if (sizeRange) {
        sizeRange.value = 50;
    }
    
    document.querySelectorAll('.size-preset').forEach(preset => {
        preset.classList.remove('active');
    });
    
    // 重新应用筛选
    applyFilters();
    
    window.FileTagManager?.showToast('已清除所有筛选条件', 'info');
}

function handleQuickFilterClick(event) {
    const chip = event.currentTarget;
    const filter = chip.dataset.filter;
    
    // 切换激活状态
    chip.classList.toggle('active');
    
    // 添加点击动画
    chip.style.transform = 'scale(0.95)';
    setTimeout(() => {
        chip.style.transform = '';
    }, 150);
    
    applyFilters();
}

function handleFilterModeChange(event) {
    const button = event.currentTarget;
    const mode = button.dataset.mode;
    
    // 更新按钮状态
    document.querySelectorAll('.mode-button').forEach(btn => {
        btn.classList.remove('active');
    });
    button.classList.add('active');
    
    applyFilters();
}

function handleTagFilterChange() {
    applyFilters();
}

function handleTypeFilterChange() {
    applyFilters();
}

function handleTimeRangeChange(event) {
    const value = event.target.value;
    const customDateRange = document.getElementById('customDateRange');
    
    if (value === 'custom') {
        customDateRange?.classList.add('active');
    } else {
        customDateRange?.classList.remove('active');
    }
    
    applyFilters();
}

function handleCustomDateChange() {
    applyFilters();
}

function handleSizeRangeChange(event) {
    const value = event.target.value;
    const sizeInMB = Math.round((value / 100) * 100);
    
    // 更新显示
    const rangeLabels = document.querySelectorAll('.range-labels span');
    if (rangeLabels.length >= 2) {
        rangeLabels[1].textContent = `${sizeInMB} MB+`;
    }
    
    applyFilters();
}

function handleSizePresetClick(event) {
    const preset = event.currentTarget;
    const size = preset.dataset.size;
    
    // 更新预设按钮状态
    document.querySelectorAll('.size-preset').forEach(p => {
        p.classList.remove('active');
    });
    preset.classList.add('active');
    
    // 更新滑块值
    const sizeRange = document.getElementById('sizeRange');
    if (sizeRange) {
        switch (size) {
            case 'small':
                sizeRange.value = 10;
                break;
            case 'medium':
                sizeRange.value = 50;
                break;
            case 'large':
                sizeRange.value = 90;
                break;
        }
        handleSizeRangeChange({ target: sizeRange });
    }
    
    applyFilters();
}

function handleSortChange() {
    applyFilters();
}

function handleSortOrderChange(event) {
    const button = event.currentTarget;
    const currentOrder = button.dataset.order;
    const newOrder = currentOrder === 'desc' ? 'asc' : 'desc';
    
    button.dataset.order = newOrder;
    
    // 更新图标
    const icon = button.querySelector('.material-icons');
    if (icon) {
        icon.textContent = newOrder === 'desc' ? 'arrow_downward' : 'arrow_upward';
    }
    
    applyFilters();
}

function handleViewToggle(event) {
    const toggle = event.currentTarget;
    const view = toggle.dataset.view;
    
    // 更新视图切换按钮状态
    document.querySelectorAll('.view-toggle').forEach(t => {
        t.classList.remove('active');
    });
    toggle.classList.add('active');
    
    // 更新文件列表视图
    const fileResults = document.querySelector('.file-results');
    if (fileResults) {
        fileResults.className = `file-results view-${view}`;
    }
    
    window.FileTagManager?.showToast(`已切换到${view === 'list' ? '列表' : '网格'}视图`, 'info');
}

function handleLoadMore() {
    const loadMoreButton = document.querySelector('.load-more-button');
    
    // 添加加载动画
    loadMoreButton.innerHTML = `
        <span class="material-icons">hourglass_empty</span>
        加载中...
    `;
    
    // 模拟加载更多文件
    setTimeout(() => {
        addMoreFiles();
        loadMoreButton.innerHTML = `
            <span class="material-icons">expand_more</span>
            加载更多
        `;
    }, 1500);
}

function addMoreFiles() {
    const fileResults = document.querySelector('.file-results');
    const newFiles = [
        {
            id: 'new1',
            name: '新文件1.jpg',
            size: '2.1 MB',
            date: '2024年1月3日',
            tags: ['photos']
        },
        {
            id: 'new2',
            name: '新文件2.png',
            size: '4.5 MB',
            date: '2024年1月2日',
            tags: ['photos', 'important']
        }
    ];
    
    newFiles.forEach(file => {
        const fileElement = createFileElement(file);
        fileResults.appendChild(fileElement);
        
        // 添加进入动画
        fileElement.style.opacity = '0';
        fileElement.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            fileElement.style.transition = 'all 0.3s ease';
            fileElement.style.opacity = '1';
            fileElement.style.transform = 'translateY(0)';
        }, 100);
    });
    
    updateResultCount();
}

function createFileElement(file) {
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.dataset.fileId = file.id;
    
    const tagsHtml = file.tags.map(tag => `<span class="tag ${tag}">${tag}</span>`).join('');
    
    fileItem.innerHTML = `
        <div class="file-icon">
            <span class="material-icons">image</span>
        </div>
        <div class="file-info">
            <h3 class="file-name">${file.name}</h3>
            <p class="file-details">${file.size} • ${file.date}</p>
            <div class="file-tags">${tagsHtml}</div>
        </div>
        <button class="icon-button file-menu">
            <span class="material-icons">more_vert</span>
        </button>
    `;
    
    // 绑定点击事件
    fileItem.addEventListener('click', window.handleFileClick || function() {});
    
    return fileItem;
}

function applyFilters() {
    const filters = collectFilterCriteria();
    const fileItems = document.querySelectorAll('.file-item');
    let visibleCount = 0;
    
    fileItems.forEach(item => {
        const shouldShow = matchesFilters(item, filters);
        
        if (shouldShow) {
            item.style.display = 'flex';
            item.style.animation = 'fadeIn 0.3s ease';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    updateResultCount(visibleCount);
    sortFiles(filters.sortBy, filters.sortOrder);
}

function collectFilterCriteria() {
    const criteria = {
        quickFilters: [],
        tags: [],
        types: [],
        timeRange: null,
        sizeRange: null,
        sortBy: 'date',
        sortOrder: 'desc'
    };
    
    // 收集快速筛选
    document.querySelectorAll('.filter-chip.active').forEach(chip => {
        criteria.quickFilters.push(chip.dataset.filter);
    });
    
    // 收集标签筛选
    document.querySelectorAll('.tag-checkbox:checked').forEach(checkbox => {
        criteria.tags.push(checkbox.id.replace('tag-', ''));
    });
    
    // 收集类型筛选
    document.querySelectorAll('.type-checkbox:checked').forEach(checkbox => {
        criteria.types.push(checkbox.id.replace('type-', ''));
    });
    
    // 收集时间范围
    const timeRange = document.querySelector('input[name="timeRange"]:checked');
    if (timeRange) {
        criteria.timeRange = timeRange.value;
    }
    
    // 收集文件大小
    const sizeRange = document.getElementById('sizeRange');
    if (sizeRange) {
        criteria.sizeRange = parseInt(sizeRange.value);
    }
    
    // 收集排序设置
    const sortBy = document.getElementById('sortBy');
    const sortOrder = document.getElementById('sortOrder');
    if (sortBy) {
        criteria.sortBy = sortBy.value;
    }
    if (sortOrder) {
        criteria.sortOrder = sortOrder.dataset.order;
    }
    
    return criteria;
}

function matchesFilters(fileItem, filters) {
    // 检查标签筛选
    if (filters.tags.length > 0) {
        const fileTags = Array.from(fileItem.querySelectorAll('.tag')).map(tag => 
            tag.className.split(' ').find(cls => cls !== 'tag')
        );
        
        const filterMode = document.querySelector('.mode-button.active')?.dataset.mode || 'any';
        
        if (filterMode === 'all') {
            // 必须包含所有选中的标签
            if (!filters.tags.every(tag => fileTags.includes(tag))) {
                return false;
            }
        } else {
            // 包含任意一个选中的标签
            if (!filters.tags.some(tag => fileTags.includes(tag))) {
                return false;
            }
        }
    }
    
    // 检查文件类型筛选
    if (filters.types.length > 0) {
        const fileIcon = fileItem.querySelector('.file-icon .material-icons')?.textContent;
        const fileType = getFileTypeFromIcon(fileIcon);
        
        if (!filters.types.includes(fileType)) {
            return false;
        }
    }
    
    // 其他筛选条件可以在这里添加
    
    return true;
}

function getFileTypeFromIcon(iconName) {
    const typeMap = {
        'description': 'document',
        'image': 'image',
        'movie': 'video',
        'music_note': 'audio',
        'picture_as_pdf': 'document',
        'table_chart': 'document'
    };
    
    return typeMap[iconName] || 'document';
}

function sortFiles(sortBy, sortOrder) {
    const fileResults = document.querySelector('.file-results');
    const fileItems = Array.from(fileResults.querySelectorAll('.file-item'));
    
    fileItems.sort((a, b) => {
        let aValue, bValue;
        
        switch (sortBy) {
            case 'name':
                aValue = a.querySelector('.file-name')?.textContent || '';
                bValue = b.querySelector('.file-name')?.textContent || '';
                break;
            case 'size':
                aValue = parseFileSize(a.querySelector('.file-details')?.textContent || '');
                bValue = parseFileSize(b.querySelector('.file-details')?.textContent || '');
                break;
            case 'date':
                aValue = new Date(a.querySelector('.file-details')?.textContent.split('•')[1]?.trim() || '');
                bValue = new Date(b.querySelector('.file-details')?.textContent.split('•')[1]?.trim() || '');
                break;
            default:
                return 0;
        }
        
        if (sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
    
    // 重新排列DOM元素
    fileItems.forEach(item => {
        fileResults.appendChild(item);
    });
}

function parseFileSize(text) {
    const match = text.match(/(\d+\.?\d*)\s*(KB|MB|GB)/);
    if (!match) return 0;
    
    const size = parseFloat(match[1]);
    const unit = match[2];
    
    switch (unit) {
        case 'KB': return size;
        case 'MB': return size * 1024;
        case 'GB': return size * 1024 * 1024;
        default: return size;
    }
}

function updateResultCount(count) {
    if (count === undefined) {
        count = document.querySelectorAll('.file-item[style*="display: flex"], .file-item:not([style*="display: none"])').length;
    }
    
    const resultCount = document.querySelector('.result-count');
    if (resultCount) {
        resultCount.innerHTML = `找到 <strong>${count}</strong> 个文件`;
    }
}
